const isProd = process.env.NODE_ENV === 'production'

module.exports = {
  extends: ['@fe/eslint-config-vue'],
  plugins: ['@fe/code-check'],
  rules: {
    '@fe/code-check/todo-check': isProd ? 2 : 0,
    '@fe/code-check/config-equal-check': isProd ? 2 : 0,
    '@fe/code-check/activty-code-check': 2,
    '@fe/code-check/project-id-check': 2,
    '@fe/code-check/test-check': 2,
  },
  globals: {
    __LOCAL__: true,
  },
}
