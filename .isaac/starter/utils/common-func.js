import request from './request'
import { getYPP } from '@fe/global'
// import html2canvas from 'html2canvas'

const YPP = getYPP()

const ANDROID_PACKAGE = {
  'bixin': 'com.yitantech.gaigai',
  'xiaoxingqiu': 'com.yangle.xiaoyuzhou',
  'yuer': 'com.yupaopao.yuer',
}

const IOS_DOWNLOAD_URL = {
  'bixin': 'https://itunes.apple.com/cn/app/id1286964732?mt=8',
  'yuer': 'https://itunes.apple.com/cn/app/id1376410848?mt=8',
  'xiaoxingqiu': 'https://itunes.apple.com/cn/app/id1344758206?mt=8',
}

const SCHEME_PREFIX = {
  'bixin': 'bixin',
  'yuer': 'yuer',
  'xxq': 'xiaoxingqiu',
}

// 跳转充值页
export const goRecharge = (rechargeType = 0) => {
  switch (YPP.mcBridge.subject) {
    case 'yuer':
      YPP.mcBridge.go(`yuer://plugin/RechargeDiamondPlugin`)
      break
    case 'xxq':
      YPP.mcBridge.go(`xiaoxingqiu://npage/recharge/entry`)
      break
    default:
      YPP.mcBridge.go(`bixin://plugin/RechargeService`, {
        rechargeType,
      })
      break
  }
}

// 跳转个人页
export const goProfile = (uid) => {
  switch (YPP.mcBridge.subject) {
    case 'yuer':
      YPP.mcBridge.go({
        android: `yuer://npage/user/profile?otherUid=${uid}`,
        iOS: `yuer://npage/user/profile?uid=${uid}`,
      })
      break
    case 'xxq':
      YPP.mcBridge.go(`xiaoxingqiu://plugin/Userdetail`, {
        uid: uid,
      })
      break
    default:
      YPP.mcBridge.go(`bixin://plugin/UserDetail`, {
        uid: uid,
      })
      break
  }
}

// 跳转聊天室(并打开礼物面板)
// params =  {
//   roomId: '',
// 需要打开礼物面板时，额外传入
//   board: 'giftBoard',
//   tabId: '',
//   giftId: '',
// }
export const goChatRoom = (params) => {
  YPP.mcBridge.go(`${SCHEME_PREFIX[YPP.mcBridge.subject]}://plugin/ChatRoomLoader`, params)
}

// 已经在聊天室内，打开礼物面板
export const openChatRoomGiftBoard = (params) => {
  if (YPP.mcBridge.app.isYppPC) {
    YPP.mcBridge.call('native_openGiftReward')
  } else {
    YPP.mcBridge.call('openRewardDialog', params)
    YPP.mcBridge.call('closeWebview')
  }
}

const getGiftBoardScheme = async (anchorUid, giftId, prefix = SCHEME_PREFIX[YPP.mcBridge.subject]) => {
  let scheme = `${prefix}://npage/live/giftSelect`
  const result = await request.post('/api/activity/goodsRemoteService/getActivityGiftV2', {
    anchorUid,
    giftId,
  })
  if (result && result.tabId) {
    scheme = scheme + `?tabId=${result.tabId}`
    if (giftId) {
      scheme = scheme + `&giftId=${giftId}`
    }
  }
  return scheme
}

const getLiveRoomScheme = async (liveRoomId, anchorUid, giftId, prefix = SCHEME_PREFIX[YPP.mcBridge.subject]) => {
  let scheme = `${prefix}://plugin/LivePlugin?liveRoomId=${liveRoomId}`
  if(anchorUid && giftId) {
    let giftScheme = await getGiftBoardScheme(anchorUid, giftId, prefix)
    giftScheme = encodeURIComponent(giftScheme)
    scheme = `${scheme}&extraAwakenScheme=${giftScheme}`
  }
  return scheme
}

// 跳转直播间(并打开礼物面板)
// liveType: 0=视频直播 1=视频录播 2=语音直播 3=语音录播
// yuerGoWhere: bixin yuer xiaoxingqiu
export const goLiveRoomAndOpenGiftBoard = async (liveRoomId, anchorUid, giftId, liveType = 0, yuerGoWhere = 'bixin') => {
  // 如果是鱼耳语音，而且是视频直播，需要跳转其它app
  if (YPP.mcBridge.subject === 'yuer' && (liveType === 0 || liveType === 1)) {
    if (YPP.mcBridge.isAndroid) {
      YPP.mcBridge.call('app_isAppInstalled', {
        package: ANDROID_PACKAGE[yuerGoWhere]
      }, async res => {
        if (res.installed) {
          YPP.mcBridge.call("download_via_browser", {
            url: await getLiveRoomScheme(liveRoomId, anchorUid, giftId, yuerGoWhere),
          })
        } else {
          YPP.mcBridge.call("page_open", {
            "url": `https://sj.qq.com/myapp/detail.htm?apkName=${ANDROID_PACKAGE[yuerGoWhere]}`,
          }, (rtn) => {
          })
        }
      })
    } else if (YPP.mcBridge.isIOS) {
      YPP.mcBridge.call("app_isAppInstalled", {
        "scheme": `${yuerGoWhere}://`,
      }, async (rtn) => {
        if (rtn.installed) {
          let scheme = await getLiveRoomScheme(liveRoomId, anchorUid, giftId, yuerGoWhere)
          window.location.href = `${scheme}&noConverted=1`
        } else {
          window.location.href = IOS_DOWNLOAD_URL[yuerGoWhere]
        }
      })
    }
  } else {
    const scheme = await getLiveRoomScheme(liveRoomId, anchorUid, giftId)
    YPP.mcBridge.go(scheme)
  }
}

// 已经在直播间内，打开礼物面板
export const openLiveRoomGiftBoard = async(anchorUid, giftId) => {
  const scheme = await getGiftBoardScheme(anchorUid, giftId)
  YPP.mcBridge.go(scheme)
}

// 正在聊天室时跳转聊天室，未在跳转个人主页
export const goChatRoomOrProfile = (uid, params) => {
  if (params) {
    YPP.mcBridge.go(`${SCHEME_PREFIX[YPP.mcBridge.subject]}://plugin/ChatRoomLoader`, params)
  } else {
    goProfile(uid)
  }
}

// 正在直播时跳转直播间，未直播跳转个人主页
export const goLiveRoomOrProfile = (uid, liveRoomId) => {
  if (liveRoomId) {
    YPP.mcBridge.go(`${SCHEME_PREFIX[YPP.mcBridge.subject]}://plugin/LivePlugin`, {
      liveRoomId,
    })
  } else {
    goProfile(uid)
  }
}
  // 获取视口高度
export const getViewPortHeight = () => {
  let height = window.document.documentElement.clientHeight || window.innerHeight
  if (YPP.mcBridge.isInYpp && !YPP.mcBridge.app.isYppPC) {
    height = YPP.mcBridge.detail.appInfo.height
  }
  return height
}

// 为个位数加前缀零
export const addPrefixZero = (value) => {
  return value <= 9 ? `0${value}` : value
}
// 防抖
export const debounce = (fn, duration = 500, immediate = true) => {
  let timer
  let isFirst = true
  function _debounce () {
    if (timer) {
      clearInterval(timer)
    }
    if (immediate) {
      timer = setTimeout(function () {
        isFirst = true
      }, duration)
      if (isFirst) {
        fn.apply(this, arguments)
        isFirst = false
      }
    } else {
      timer = setTimeout(function () {
        fn.apply(this, arguments)
      }, duration)
    }
  }
  return _debounce
}

// 节流
export const throttle = (fn, duration = 500) => {
  let preTime = 0
  function _throttle () {
    let now = Date.now()
    if (now - preTime > duration) {
      fn.apply(this, arguments)
      preTime = now
    }
  }
  return _throttle
}

// 将750设计稿宽度下的px值 转化为 vw值
export const pxBase750ToVw = (val) => {
  return val * 100 / 750
}

// 将750设计稿宽度下的px值 转化为 当前视口下的px值
export const pxBase750ToPxBaseViewPort = (val) => {
  const clientWidth = window.innerWidth || screen.availWidth
  return val * clientWidth / 750
}

// 页面元素生成图片
// export const createCanvasCard = ($el = document.body, ignoreClass, backgroundColor) => {
//   return new Promise((resolve, reject) => {
//     html2canvas($el, {
//       allowTaint: true, // 允许跨域
//       useCORS: true, // 解决污染画布问题
//       scale: 3,
//       backgroundColor,
//       // 忽略元素
//       ignoreElements (el) {
//         return el.className.includes(ignoreClass)
//       },
//     }).then(async canvas => {
//       const str = canvas.toDataURL('image/png', 1.0)
//       if (!str) { // 容错
//         reject(false)
//         return false
//       }
//       const base64ImgStr = str.slice(str.indexOf('base64') + 7) // 截取”data:image/png;base64,“后的字符串
//       const token = await request.get('/common/qiniu/file/token')
//       const url = 'https://upload.qiniup.com/putb64/-1' // https解决ios线上上传报错问题
//       const xhr = new XMLHttpRequest()
//       xhr.open('POST', url, true)
//       xhr.setRequestHeader('Content-Type', 'application/octet-stream')
//       xhr.setRequestHeader('Authorization', `UpToken ${token}`)
//       xhr.send(base64ImgStr)
//       xhr.onreadystatechange = () => {
//         if (xhr.readyState === 4) {
//           const result = JSON.parse(xhr.responseText)
//           const cardImgUrl = `https://yphoto.eryufm.cn/${result.key}`
//           resolve(cardImgUrl)
//           return cardImgUrl
//         }
//       }
//     }, err => {
//       reject(err)
//       return false
//     })
//   })
// }