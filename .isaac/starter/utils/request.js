import Request from '@fe/request'
import { Toast, PayBlockModal, ConfirmModal } from '@fe/lego'
import { getYPP } from '@fe/global'
import { arthurVersionBlock } from './compat'
import requestConfig from '@host/common/config/request'
import { interceptor } from '@fe/nash-api-core'

const { env } = getYPP()
const request = new Request({
  credentials: 'include',
  showError: true,
  ...requestConfig.options,
})

request.interceptors.request.use(async (options) => {
  // 中心化网关版本阻断
  if (options.useArthur && arthurVersionBlock()) {
    throw '当前APP版本过低，请升级后重试'
  }
  // 提示加载中
  options.showLoading && Toast.showLoading()

  options = requestConfig.requestInterceptor(options)

  if (__SSR__ === 'server') {
    // 服务端渲染需要使用绝对路径，否则会找不到请求地址
    if (!/^\w+:\/\//.test(options.url)) {
      options.url =
        (options.useArthur ? '' : env.ssr.requestPrefix) + options.url
    }

    options.headers = { 'User-Agent': env.ssr.userAgent, ...options.headers }
  }

  return options
})

// 响应拦截器
request.interceptors.response
  .use(requestConfig.responseInterceptor)
  .use((body, opts) => {
    if (opts.skipDefaultInterceptor) {
      return body
    }
    if (!body) {
      return Promise.reject(undefined)
    }
    if (body && 'code' in body) {
      const { code, result, msg, ext } = body

      // 自定义成功code
      const successCodes = [
        ...(requestConfig.successCodes || ['8000', 8000]),
        ...(opts.successCodes || []),
      ]
      if (successCodes.indexOf(code) !== -1) {
        return result
      }

      // code:9011 type: 101-短信 102-扫脸 100-滑块 抛异常拦截, request已处理跳转
      if (!YPP.app.isYppPC && ['9011', 9011].includes(code) &&  ext && [101, 102, 100].includes(Number(ext.certType))) {
        return new Promise((resolve, reject) => {
          reject({ msg, code })
        })
      }
      // 实名认证拦截
      if (['9011', 9011].includes(code) && ext && Number(ext.certType) === 103) {
        const { PCMessage, certification } = ext
        if (YPP.app.isYppPC) {
          return Promise.reject(msg || PCMessage || '请先实名认证～')
        }

        const realNameGuide = new Promise((resolve, reject) => {
          let certify = {}
          let btnInfo = {}
          try {
            certify = JSON.parse(certification)
            btnInfo = certify.buttonModelList.find(item => item.type === 'MAIN') || {}
          } catch (e) {}

          const params = {
            title: '实名认证',
            content: certify.content,
            theme: YPP.app.isYuer ? 'yuer' : 'bixin',
            btns: [
              {
                text: btnInfo.title,
                onClick: () => {
                  YPP.mcBridge.go(`${YPP.app.name === 'xxq' ? 'xiaoxingqiu' : YPP.app.name}://webpage/push?yppHideNavBar=1&url=${btnInfo.scheme || `https://${YPP.env.prefix}web.bixin.cn/id-subway`}`)
                  setTimeout(() => {
                    ConfirmModal.close()
                    reject({ msg, code })
                  }, 800)
                },
              },
            ],
            onClose: async () => {
              reject({ msg, code })
            },
          }

          ConfirmModal.show(params)
        })
        return realNameGuide
      }

      // 大额支付拦截
      if (['9012', 9012].includes(code)) {
        const paymentRes = new Promise((resolve, reject) => {
          const params = {
            requestCfg: opts,
            ...(body.ext || {}),
            // 点击支付回调
            onConfirm: async () => {
              const method = (opts.method || '').toLowerCase()
              const requestBody = JSON.parse(opts.body || '{}')
              if (typeof request[method] === 'function') {
                const res = await request[method](
                  opts.url,
                  {
                    ...requestBody,
                    payExt: { riskExt: { riskTraceId: body.ext.riskTraceId } },
                    riskTraceId: body.ext.riskTraceId,
                  },
                  opts
                )
                resolve(res)
              }
            },
            onClose: async () => {
              reject(undefined)
            },
          }
          // 展示支付拦截modal
          PayBlockModal.show(params)
        })
        return paymentRes
      }

      return Promise.reject({ msg, code } || '接口异常')
    } else {
      return body
    }
  })
  .catch(({ msg, code }, opts) => {
    if (!msg) return
    // 网络问题会报上错
    if (
      typeof msg === 'string' &&
      msg.indexOf('TypeError: Failed to fetch') >= 0
    )
      return

    opts.showError &&
      Toast.show({
        content: msg,
        duration: 2000,
        position: 'middle',
      })
  })
  .finally(({ showLoading }) => {
    showLoading && Toast.close()
  })

export const _request = ((request) => {
  return (url, params, options) => {
    let abort = null
    const abortPromise = new Promise((resolve, reject) => {
      abort = () => {
        reject('abort.')
      }
    })
    const promise = Promise.race([
      request.post(url, params, options),
      abortPromise,
    ])
    promise.abort = abort
    return promise
  }
})(request)

interceptor((path, params, options) => {
  return request.post(path, params, { useArthur: true, ...options })
})

export default request
