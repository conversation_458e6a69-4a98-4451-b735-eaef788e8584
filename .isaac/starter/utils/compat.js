import { getYPP } from '@fe/global'
import compareVersions from 'compare-versions'
/*
 * 兼容相关的函数
 */

/**
 * 老项目采用了 hash 方式做路由，后期由于需要采用服务端，所以统一使用了 history 方式的路由，
 * 但有一些老的链接还是带 hash 的，需要使它们自动跳转到不带 hash，而带 history 的路由
 */
export function shouldHashRouterUpdate() {
  const hasInitState = typeof window !== 'undefined' && ('__INITIAL_STATE__' in window)

  return __SSR__ && hasInitState && location.hash.startsWith('#/') && location.hash !== '#/' // 首页为 hash 时不需要迁移
}

/**
 * Arthur中心化网关阻断器
 */
const { mcBridge: bridge, app, env } = getYPP()
const ARTHUR_VERSION = {
  'bixin': '7.2.0',
  'yuer': '5.3.0',
  'xxq': '3.36.1',
  'pc-chatroom': '6.2.6',
  'pc-live': '1.6.6',
}
export function arthurVersionBlock() {
  // 服务端渲染跳过
  if (__SSR__ === 'server') return false
  // 解析不正常跳过
  if (!bridge || !app || !env) return false
  // 测试环境跳过
  if (env.isTest) return false
  // 非站内情况跳过
  if (!bridge.isInYpp || !bridge.detail) return false

  // 开始判断
  const appName = app.name
  const bundleVersion = bridge.detail.bundle.version
  const authorVersion = ARTHUR_VERSION[appName]
  if (!authorVersion || !bundleVersion) return false
  // 助手直接跳过 现有场景全部可用
  if ((bridge.detail.bundle.id + '').indexOf('helper') > -1) return false

  if (compareVersions(bundleVersion, authorVersion) === -1) return true
  return false
}
