import Vue from 'vue'

/** 
 * 通用网关接口：可查看文档：https://alidocs.dingtalk.com/i/nodes/qXomz1wAyjKVXPr3oDPEV3Y9pRBx5OrE?dontjump=true&corpId=ding12daad541e02ab5135c2f4657eb6378f 
*/
export default {
  // 获取系统时间（需要验签和加解密，仅站内使用）
  getSystemTime: {
    default: {
      systemTime: 0,
    },
    action: async (context, payload) => await Vue.$request.post('/openapi/arthurTools/mobile/time', {}, { useArthur: true }),
    mutation: (state, payload) => state.systemTime = payload && Number(payload.currentTimeMillis),
  },

  // 获取系统时间（跳过验签和加解密，站内外均可使用）
  getSystemTimeOutside: {
    default: {
      systemTimeOutside: 0,
    },
    action: async (context, payload) => await Vue.$request.post('/openapi/arthurTools/time', {}, { useArthur: true, skipDecrypt: true }),
    mutation: (state, payload) => state.systemTimeOutside = payload && Number(payload.currentTimeMillis),
  },

  // 获取用户信息（需要验签和加解密，仅站内使用）
  getUserInfo: {
    default: {
      userInfo: {},
    },
    action: async (context, payload) => await Vue.$request.post('/commonUser/query/user/profile', {}, { useArthur: true }),
    mutation: (state, payload) => state.userInfo = payload || {},
  },

  // 获取用户信息（跳过验签和加解密，站内外均可使用）：仅返回昵称和头像
  getUserInfoOutside: {
    default: {
      userInfoOutside: {},
    },
    action: async (context, payload) => await Vue.$request.post('/userInfoQueryGatewayService/outside/getUserInfo', { ...payload, scene: 99 }, { useArthur: { outside: true } }),
    mutation: (state, payload) => state.userInfoOutside = payload || {},
  },

  // 获取用户钻石余额
  getUserAccount: {
    default: {
      diamondAmount: 0,
    },
    action: async (context, payload) => await Vue.$request.post('/v3/user/diamond/info', {}, { useArthur: true }),
    mutation: (state, payload) => state.diamondAmount = payload && payload.diamondAmount ? payload.diamondAmount : 0,
  },

  // 获取用户当前所在聊天室信息（hostUid 、roomId和房间类型templet，没在聊天室不返回）
  getUserCurrentRoomInfo: {
    default: {
    	hostUid: '', // 主播id
      roomId: '',  // 房间id
      roomTemplet: 0,  // 房间类型
    },
    action: async (context, payload) => await Vue.$request.post('/activity-slong/CommonService/getUserCurrentRoomInfo', {}, { useArthur: true }),
    mutation: (state, payload) => {
    	state.roomId = payload.chatroomBaseInfoDTO?.roomId
      state.roomTemplet = payload?.chatroomBaseInfoDTO?.templet
      payload.seatInfoList && payload.seatInfoList.map(item => {
        if (item.seatIndex === 'master') {
        	state.hostUid = item.userSimpleInfo?.uid
        }
      })
    },
  },

  // 关注好友（站内外接口仅路径不同，参数和返回值相同）
  followUser: {
    default: {},
    action: async (context, payload) => await Vue.$request.post(`/activity-slong/CommonService/${YPP.app.isYpp ? 'followUser' : 'outsidefollowUser'}`, payload, { useArthur: true }),
    mutation: (state, payload) => {},
  },

  // 取消关注好友（站内外接口仅路径不同，参数和返回值相同）
  cancelFollow: {
    default: {},
    action: async (context, payload) => await Vue.$request.post(`/activity-slong/CommonService/${YPP.app.isYpp ? 'cancelFollow' : 'outsideCancelFollow'}`, payload, { useArthur: true }),
    mutation: (state, payload) => {},
  },

  // 收藏聊天室（仅支持站内）
  userCollectionRoom: {
    default: {},
    action: async (context, payload) => await Vue.$request.post('/activity-slong/CommonService/userCollectionRoom', payload, { useArthur: true }),
    mutation: (state, payload) => {},
  },


  // 赛段配置（站内外路径不同）
  getStageList: {
    default: {
    },
    action: async (context, payload) => {
      // 参数 {sceneIds: [活动后台赛段配置id数组]}
      return await Vue.$request.post(YPP.app.isYpp ? '/competition-component/competitionStageList' : `/competition-component-web/competitionStageList`, payload, { 
        useArthur: YPP.app.isYpp,
        useWebApi: !YPP.app.isYpp,
       })
    },
    mutation: (state, payload) => {},
  },

}
