// 重写点击埋点方法，针对
export const rewriteTrack = () => {
  const originTrack = YPP.track
  const isUndef = value => typeof value === 'undefined'

  YPP.track = (elementId = '', payload = {}) => {
    /* eslint-disable camelcase */
    const { project_sign, activity_id } = window.kelvin_config || {}

    let elementObj = {}
    const activityPath = window.location.pathname
    const itemName = (window.kelvin_config && window.kelvin_config.name) || 'unknow'

    try {
      elementObj = JSON.parse(elementId)
    } catch (err) {
    }

    /**
     * 1 区分是否需要重置
     * 2 区分是开发页面还是kelvin页面
     */
    if (!Object.keys(elementObj).length) {
      if ((elementId || '').includes('ElementId-')) { // 原始埋点不需要重置
        originTrack.apply(YPP, [elementId, payload])
        return
      } else {
        elementObj = {
          activityButtonName: elementId, // 兼容 YPP.track('按钮新埋点名称')
        }
      }
    }
    if (window.autotrack) { // 开发页面
      const activityButtonName = elementObj.activityButtonName // 点击事件
      activityButtonName && window.autotrack.track(activityButtonName, payload)
      return
    }

    const trackParams = {
      activityPath,
      itemName,
      ...elementObj,
      ...payload,
    }

    if (isUndef(trackParams.itemId)) {
      /* eslint-disable camelcase */
      trackParams.itemId = project_sign
    }

    if (isUndef(trackParams.activityId)) {
      trackParams.activityId = activity_id || 0
    }

    if (isUndef(trackParams.activityPageName)) {
      const pageConfig = window.kelvin_config || { pages: [] }
      const { pageId } = window.__$route?.params || {}
      const id = pageId || trackParams.pageId

      const curPageConf = pageConfig.pages.find((it) => it.id === Number(id)) || {}

      const pageName = curPageConf.name

      trackParams.activityPageName = pageName
    }

    delete trackParams.elementId
    delete trackParams.pageId

    console.log('点击埋点信息', elementObj.elementId, JSON.stringify(trackParams, null, 2))

    elementObj.elementId && originTrack.apply(YPP, [elementObj.elementId, trackParams])
  }
}
