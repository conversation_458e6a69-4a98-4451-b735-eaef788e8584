export default {
  mounted() {
    if (this.preloadResources) {
      this.__preloadSid = setTimeout(() => {
        const fn = () => this.preload(this.preloadResources)
        if (window.requestIdleCallback) {
          window.requestIdleCallback(fn)
        } else {
          fn()
        }
      }, 1000)
    }
    // 预加载背景图
    if (this.preloadBgResources) {
      this.__preloadBg = setTimeout(() => {
        const fn = () => this.preloadBg(this.preloadBgResources)
        if (window.requestIdleCallback) {
          window.requestIdleCallback(fn)
        } else {
          fn()
        }
      }, 1000)
    }
  },
  beforeDestroy() {
    if (this.__preloadSid) clearTimeout(this.__preloadSid)
    if (this.__preloadBg) clearTimeout(this.__preloadBg)
  },

  methods: {
    preload(resources) {
      setTimeout(() => {
        resources.forEach(src => {
          const img = new Image()
          if (__DEV__) {
            img.onerror = e => console.error(e)
          }
          img.src = src
        })
      })
    },
    preloadBg(resources) {
      setTimeout(() => {
        resources.forEach(src => {
          const link = document.createElement('link')
          link.rel = 'preload'
          link.href = src
          link.as = 'image'
          document.body.appendChild(link)
        })
      })
    },
  },
}
