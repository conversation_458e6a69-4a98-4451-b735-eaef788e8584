export default {
  data () {
    // query.fullScreen为客户端全屏高度半屏标志，meta.fullScreen可本地调试
    const fullScreen = !!this.$route.query.fullScreen || (this.$route.meta.fullScreen && __LOCAL__)
    const isConfession = !!this.$route.query.isConfession
    return {
      fullScreen,
      isConfession,
    }
  },
  computed: {
    isHalfFullPage () {
      let isNewVer = false
      const verStr = YPP.mcBridge.detail?.bundle?.version || ''
      if (YPP.app.isYppPC) {
        // PC 6.5.3.5（6510支持挂件半屏，6535支持其他半屏）
        isNewVer = this.versionStringCompare(verStr, '6.5.3.5')
      } else if (YPP.app.isYuer) {
        // 语音 5.32.0
        isNewVer = this.versionStringCompare(verStr, '5.32.0')
      } else if (YPP.app.isBixin) {
        // 比心 8.9.2
        isNewVer = this.versionStringCompare(verStr, '8.9.2')
      } else if (YPP.app.isMvp) {
        // MVP 1.2.9
        isNewVer = this.versionStringCompare(verStr, '1.2.9')
      }

      /**
       * ios 告白位兼容
       */
      let isNewConfession = false
      if (YPP.app.isYuer) {
        // 语音 5.40.0
        isNewConfession = this.versionStringCompare(verStr, '5.40.0')
      } else if (YPP.app.isBixin) {
        // 比心 8.11.6
        isNewConfession = this.versionStringCompare(verStr, '8.11.6')
      } else if (YPP.app.isMvp) {
        // MVP 1.4.9
        isNewConfession = this.versionStringCompare(verStr, '1.4.9')
      }

      return isNewVer && this.fullScreen && !(YPP.platform.isIOS && this.isConfession && !isNewConfession)
    },
    isSmallSize () {
      if (YPP.app.isYppPC) return false
      const width = window.screen.availWidth || window.screen.width
      const height = window.screen.availHeight || window.screen.height
      return height / width <= 1.78 // 667/375 = 1.778666
    },
    isPcConfession () {
      /**
       * pc 告白位兼容(尺寸)
       */
      const verStr = YPP.mcBridge.detail?.bundle?.version || ''
      return YPP.app.isYppPC && this.isConfession && !this.versionStringCompare(verStr, '6.5.8.5')
    },
  },
  methods: {
    // 版本号比较
    versionStringCompare (preVersion = '', lastVersion = '') {
      const sources = preVersion.split('.')
      const dests = lastVersion.split('.')
      const maxL = Math.max(sources.length, dests.length)
      let result = 0
      for (let i = 0; i < maxL; i++) {
        const preValue = sources.length > i ? sources[i] : 0
        const preNum = isNaN(Number(preValue)) ? preValue.charCodeAt() : Number(preValue)
        const lastValue = dests.length > i ? dests[i] : 0
        const lastNum = isNaN(Number(lastValue)) ? lastValue.charCodeAt() : Number(lastValue)
        if (preNum < lastNum) {
          result = -1
          break
        } else if (preNum > lastNum) {
          result = 1
          break
        }
      }
      return [0, 1].includes(result)
    },
  },
}
