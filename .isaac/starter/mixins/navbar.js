export default {
  methods: {
    __updateNavbar() {
      if (this.navbarProps) {
        this.$emit('updateNavbar', this.navbarProps)
      }
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.__updateNavbar()
    next()
  },
  created() {
    // 只有在 data 中设置了 navbarProps 才自动设置
    if (this.navbarProps) {
      this.$watch('navbarProps', props => this.$emit('updateNavbar', props))
    }
    this.__updateNavbar()
  },
  activated() {
    this.__updateNavbar()
  },
}
