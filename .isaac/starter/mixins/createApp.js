import { shouldHashRouterUpdate } from '../utils/compat'

/** 挂载在 app.vue 的 mixin */
export default function (defaultNavbarProps) {
  return {
    data() {
      return {
        globalNavbarProps: defaultNavbarProps,
      }
    },
    created() {
      const updateNavbarFromRoute = (route) => {
        if (!route) return
        // 获取路由中的 navbar 配置
        const { navbarProps = {} } = route.meta || {}
        this.globalNavbarProps = { ...defaultNavbarProps, ...navbarProps, ...(this.$bridge.app.isYppPC ? { hideCloseButton: true } : {})  }
        if (this.globalNavbarProps.title !== undefined && typeof document !== 'undefined') document.title = this.globalNavbarProps.title
      }

      // 首个路由无法在下面的 beforeEach 中监听到，需要手动监听
      updateNavbarFromRoute(this.$route)

      // 每次跳转到一个新的页面都要恢复默认的路由配置
      // FIXED: 会先将 scrollTop 设置成下一个页面的位置，就导致当前 navbar 状态变化，需要换成 afterEach
      // this.$router.beforeEach((to, from, next) => {
      //   updateNavbarFromRoute(to)
      //   next()
      // })
      this.$router.afterEach((to, from) => {
        updateNavbarFromRoute(to)
      })
    },
    mounted() {
      // 老页面的兼容
      if (shouldHashRouterUpdate()) {
        this.$router.replace(location.hash.substr(1))
      }

      // 勿删：性能打点用
      window.YPP_PERF_LAUNCH_MOUNTED = true
    },
    methods: {
      updateGlobalNavbar(newProps) {
        this.globalNavbarProps = { ...this.globalNavbarProps, ...newProps, ...(this.$bridge.app.isYppPC ? { hideCloseButton: true } : {}) }
        if (this.globalNavbarProps.title !== undefined && typeof document !== 'undefined') document.title = this.globalNavbarProps.title
      },
    },
  }
}
