import Vue from 'vue'
import Router from 'vue-router'
import { setNavbarScrollTop } from '@fe/lego'
import ROUTER_OPTIONS from '@host/router'
import { getYPP } from '@fe/global'

Vue.use(Router)

// const routes = ROUTER_OPTIONS.routes || []
const { env } = getYPP()

export function createRouter() {
  return new Router({
    base: env.ssr.routerBase,
    mode: 'history',
    ...ROUTER_OPTIONS,
    scrollBehavior (to, from, savedPosition) {
      // 解决 navbar 在切换页面时闪动的问题
      setNavbarScrollTop(savedPosition && savedPosition.y || 0)
      if (savedPosition) return savedPosition
      return {x: 0, y: 0}
    },
  })
}
