import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedState from '@fe/vuex-persistedstate'
import STORE_OPTIONS from '@host/store'

const { persisted, expired } = STORE_OPTIONS
delete STORE_OPTIONS.persisted
delete STORE_OPTIONS.expired

Vue.use(Vuex)

/**
 * @type import('vuex').StoreOptions
 */
const storeOptions = {
  strict: true,
  devtools: true,
  plugins: [],
  modules: {},
  ...STORE_OPTIONS,
}
const storeExpired = expired || 7 * 24 * 60 * 60 * 1000

// store 数据清理策略
function storeCacheCleaner(storage) {
  if (!storage) storage = window && window.localStorage
  if (!storage) return
  const cacheKey = `${__PROJECT__}_cache_time`
  const cacheTime = Number(storage.getItem(cacheKey) || 0)
  const nowTime = Date.now()

  if (!(nowTime - cacheTime < storeExpired)) {
    // 清理
    Object.keys(storage).forEach(s => {
      if (s.startsWith(__PROJECT__)) storage.removeItem(s)
    })
    // 设置新记录
    storage.setItem(cacheKey, nowTime)
  }
}

// 服务端渲染不需要 vuex-persistedstate 缓存数据，默认初始数据都是从服务端来的
if (__SSR__ !== 'server' && persisted) {
  // 运行清理器
  storeCacheCleaner()
  let key
  if (navigator && navigator.userAgent) {
    const md5 = require('js-md5')
    const hash = md5(navigator.userAgent)
    key = `${__PROJECT__}_${hash}`
  } else {
    key = __PROJECT__
  }
  storeOptions.plugins = [createPersistedState({ key })]
}

export function createStore() {
  return new Vuex.Store({
    ...storeOptions,
    ...STORE_OPTIONS,
  })
}
