import Vue from 'vue'

import '../node-mock/basic'

const { createYPP } = require('@fe/global/lib/node')

// This exported function will be called by `bundleRenderer`.
// This is where we perform data-prefetching to determine the
// state of our application before actually rendering it.
// Since data fetching is async, this function is expected to
// return a Promise that resolves to the app instance.
export default context => {

  if (!context.fullUrl || !context.userAgent) throw new Error(`服务端渲染需要参数当前 fullUrl 和 userAgent`)

  const YPP = createYPP(context)

  if (global) global.YPP = YPP

  Vue.use(YPP)

  return new Promise((resolve, reject) => {
    const s = __DEV__ && Date.now()

    // 要在生成了 YPP 之后再加载 createApp
    const { createApp } = require('./create-app')
    const { app, router, store } = createApp()

    const routerPath = YPP.env.ssr.routerPath
    const { fullPath } = router.resolve(routerPath).route

    if (fullPath !== routerPath) {
      return reject({ fullPath, routerPath, code: 404, message: 'Route not match' })
    }

    // set router's location
    router.push(routerPath).catch(e => {
      console.log('router push error', e)
    })

    // wait until router has resolved possible async hooks
    router.onReady(() => {
      const matchedComponents = router.getMatchedComponents()
      // no matched routes
      if (!matchedComponents.length) {
        return reject({ code: 404, message: 'Page not found' })
      }
      // Call fetchData hooks on components matched by the route.
      // A preFetch hook dispatches a store action and returns a Promise,
      // which is resolved when the action is complete and store state has been
      // updated.
      Promise.all(matchedComponents.map(({ asyncData }) => asyncData && asyncData({
        store,
        route: router.currentRoute,
      }))).then(() => {
        if (__DEV__) {
          console.log('----------------------------------')
          console.log(` > data pre-fetch: ${Date.now() - s}ms`)
          console.log(' > state:')
          console.log(store.state)
          console.log('----------------------------------')
        }
        // After all preFetch hooks are resolved, our store is now
        // filled with the state needed to render the app.
        // Expose the state on the render context, and let the request handler
        // inline the state in the HTML response. This allows the client-side
        // store to pick-up the server-side state without having to duplicate
        // the initial data fetching on the client.
        context.state = store.state
        resolve(app)
      }).catch(reject)
    }, reject)
  })
}
