import Vue from 'expose-loader?exposes=Vue|default!vue'
import { busPlugin } from '../plugins/bus-plugin'
import { rewriteTrack } from '../utils/rewrite-track'
import preload from '../mixins/preload'
import { createApp } from './create-app'
import trackConfig from '@host/common/config/track'
import TEST from '@host/common/globalConfig/test'
import UAT from '@host/common/globalConfig/uat'
import PROD from '@host/common/globalConfig/prod'
import ROUTER_OPTIONS from '@host/router'

// 重写YPP.track方法，兼容搭建组件埋点
Vue.mixin({
  mounted () {
    // 时机为加载KelvinDeepRender组件
    if (this.$options._componentTag === 'KelvinDeepRender') {
      rewriteTrack() // 防止YPP.track又被eden里的global覆盖
    }
  }
})

// 派发自定义事件：load_window_vue
const event = new CustomEvent('load_window_vue', {
  detail: { vue: true },
  bubbles: true,
  cancelable: false,
})
document.dispatchEvent(event)

// 挂载vue原型$config配置
const appConfig = YPP.env.isProd ? PROD : (YPP.env.isUat ? UAT : TEST)
Vue.prototype.$config = appConfig
window.activityCode = appConfig?.activityCode || ''

// 注入autotrack自动埋点配置
const { activityId, activityCode } = appConfig || {}
window.autotrack && window.autotrack.trackConfig({
  autoTrackConfig: {
    activityId,
    activityCode,
    routes: ROUTER_OPTIONS.routes,
  },
  volcConfig: {
    targetAppName:  trackConfig.targetAppName || 'yuer', // targetAppName 指定埋点数据上报到火山上的对应业务线  如bixin 上报到火山的比心业务上
    platformCode: trackConfig.targetAppName || 'yuer', // platformCode 指定埋点数据上报到火山上的对应平台  如bixin 上报到火山的比心业务上
  },
})

// 注入预加载
Vue.mixin(preload)

// 注入全局对象 $hybrid/$bridge
Vue.use(YPP)

// 注入全局自定义eventBus（可以与kelvin搭建项目进行全局事件通信）
Vue.use(busPlugin)

function formatComponentName (vm) {
  if (vm.$root === vm) return 'root'

  const name = vm._isVue
    ? (vm.$options && vm.$options.name) ||
    (vm.$options && vm.$options._componentTag)
    : vm.name
  return (
    (name ? 'component <' + name + '>' : 'anonymous component') +
    (vm._isVue && vm.$options && vm.$options.__file
      ? ' at ' + (vm.$options && vm.$options.__file)
      : '')
  )
}

// 错误上报
Vue.config.errorHandler = function (err, vm, info) {
  console.error(err, vm, info)
  window.owl && window.owl.trackError(err, 'js_error'
    , formatComponentName(vm), { info })
}

// 输出当前项目版本号，方便验证代码发布到线上有没发布成功
window.VERSION = __VERSION__

// https://app.yupaopao.com/#/statlist
if (trackConfig.projectId) YPP.trackConfig({ 
  ...trackConfig,
  volcConfig: {
    disabled: true, // 关闭global火山引擎
  },
 })

// 兼容 iOS 伪类
document.body.addEventListener('touchstart', () => {})

// 兼容弹窗键盘唤起后 页面不会落问题
document.body.addEventListener('focusout', () => {
  if (document.body.scrollHeight <= (window.innerHeight || document.documentElement.clientHeight)) {
    window.scroll(0, 0)
  }
})

const { app, router, store } = createApp()


// prime the store with server-initialized state.
// the state is determined during SSR and inlined in the page markup.
if (window.__INITIAL_STATE__) {
  store.replaceState(window.__INITIAL_STATE__)
}

const getAsyncData = (instance) => {
  const { $options = {}, $vnode = {} } = instance
  // 只处理 router 下的组件（保证客户端和服务端的统一）
  if ($options.asyncData && $vnode.data && $vnode.data.routerView) {
    return $options.asyncData
  }
}
Vue.mixin({
  // 路由不变，但路由参数变化时  =>  重新获取远程数据
  beforeRouteUpdate (to, from, next) {
    // 获取数据的同时跳转到下一页 (也可以做到先获取好数据再跳转)
    const asyncData = getAsyncData(this)
    if (asyncData) {
      asyncData({ store: this.$store, route: to })
    }
    next()
  },
  // 路由变化时  =>  重新获取远程数据
  mounted() {
    const asyncData = getAsyncData(this)
    if (asyncData) {
      asyncData({ store: this.$store, route: this.$route })
    }
  },
})

router.onReady(() => app.$mount('#app'))
