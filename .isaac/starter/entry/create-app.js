import Vue from 'vue'
import App from '@host/index'
import { ModalPlugin } from '@fe/lego'
import { createRouter } from '../router'
import { createStore } from '../store'
import request from '../utils/request'
import navbar from '../mixins/navbar'

// 将 dialog 指定为 true 会自动注入 `v-dialog` 标签，
// 并且可以使用 `this.$dialog.show()` 来打开注入的 `v-dialog`
Vue.use(ModalPlugin, { dialog: __SSR__ !== 'server' })
Vue.use(request)

// 全局 mixin
Vue.mixin(navbar)
export function createApp() {
  const router = createRouter()
  const store = createStore()

  const app = new Vue({
    router,
    store,
    render: h => h(App),
  })

  return { app, router, store }
}
