{"name": "aquaman-ai-test", "version": "1.0.0", "description": "【海王1.0】AI研发流程测试", "sideEffects": false, "lint-staged": {"./src/**/*.{js,vue}": ["npm run lint", "git add"]}, "prettier": {"htmlWhitespaceSensitivity": "ignore", "printWidth": 140}, "scripts": {"lint": "eslint --ext .js,.vue ./src", "lint:prod": "export NODE_ENV=production && eslint --ext .js,.vue ./src", "lint:fix": "eslint --ext .js,.vue ./src --fix", "build:test": "isaac-starter build --mode development", "build:test:ssr": "isaac-starter build --mode development --ssr", "build:prod": "isaac-starter build --mode production", "build:prod:ssr": "isaac-starter build --mode production --ssr", "release:test": "isaac release test", "release:test:ssr": "isaac release test --ssr", "release:prod": "isaac release prod", "release:prod:ssr": "isaac release prod --ssr", "ssr:vue": "isaac-starter ssr:vue", "isaac:prerelease": "npm run lint", "isaac:postrelease": "isaac-starter manifest", "dev": "isaac-starter dev", "inject": "isaac-starter inject", "postinstall": "cursor-rules init -s vue2-sketch"}, "publishConfig": {"access": "public", "registry": "https://repo.yupaopao.com/artifactory/api/npm/npm-repo/"}, "license": "MIT", "dependencies": {"@fe/dragon": "^2.0.0", "@fe/eden": "^4.3.86", "@fe/global": "^3.16.0", "@fe/lego": "^4.0.0", "@fe/nash-api": "^1.0.0", "@fe/request": "^3.4.51", "@fe/utils": "^4.0.9", "@fe/vuex-persistedstate": "^1.0.0", "query-string": "^5.1.1", "vue": "2.6.12", "vue-router": "^3.4.9", "vue-runtime-helpers": "^1.1.2", "vuex": "^3.6.0", "vuex-action": "^1.2.1"}, "devDependencies": {"@fe/cursor-rules": "^1.0.0", "@babel/runtime": "^7.12.5", "@babel/runtime-corejs3": "^7.12.5", "@commitlint/cli": "^9.0.0", "@commitlint/config-conventional": "^9.0.0", "@fe/eslint-config-vue": "^1.2.13", "@fe/eslint-plugin-code-check": "^0.1.1", "@isaac/plugin-vue": "^1.1.19", "@isaac/plugin-vue-yuer": "^1.0.21", "@isaac/polyfill": "^1.0.0-beta.3", "@isaac/starter": "^1.0.0-beta.52", "core-js": "^3.8.1", "husky": "^4.3.6", "lint-staged": "^10.5.3", "postcss": "^8.1.10", "webpack": "^5.8.0", "shelljs": "^0.8.4"}, "author": "sunbinbin<<EMAIL>>", "engines": {"node": ">=10.0.0", "isaac": ">=1.4.1"}, "isaac": {"buPrefix": "yuer"}}