---
description: 生成页面plan时的规范和格式要求，包括接口使用规范
alwaysApply: false
---

# Plan文档生成规范

## 使用场景和前置条件

### 开发材料要求
生成plan文档前，开发同学需要提供以下材料：

#### 必需材料（plan生成阶段）
- **PRD（产品需求文档）**：包含完整的业务逻辑、功能需求、用户场景等
- **交互图/原型图**：包含页面布局、交互流程、状态变化等设计稿

#### 可选材料（初期可缺失，后续补充）
- **接口文档**：包含真实接口定义、参数、响应格式等
  - 缺失时：plan中标注"待后端提供接口"，保留功能设计
  - 补充后：迭代更新plan，将模拟数据方案替换为真实接口对接
- **UI设计稿**：包含视觉设计、组件样式、交互细节等
  - 补充时机：在实际执行"生成页面结构和样式"任务时提供

### 信息充分性检查
在生成plan前，必须检查以下要素：

#### 业务逻辑完整性
- ✅ PRD中包含完整的功能描述和业务流程
- ✅ 明确的用户角色和权限说明
- ✅ 清晰的数据流转和状态变化逻辑

#### 交互设计完整性
- ✅ 原型图覆盖所有页面和状态
- ✅ 交互流程和页面跳转关系清晰
- ✅ 异常情况和边界场景的处理方式

#### 技术对接完整性
- ✅ 已提供的接口文档格式规范、信息完整
- ✅ 缺失接口的功能需求明确，便于后续对接
- ✅ 前后端数据交互逻辑清晰

### 信息不足处理
当提供的材料不足时，需要明确提醒开发同学补充：

**PRD不完整时**：
- ❌ "业务逻辑描述不清晰，请补充[具体缺失的功能点]的详细说明"
- ❌ "用户权限和角色定义不明确，请补充权限控制逻辑"

**交互图不完整时**：
- ❌ "缺少[具体页面]的交互设计，请补充原型图"
- ❌ "页面状态转换不清晰，请补充[具体状态]的交互流程"

**接口信息不足时**：
- ⚠️ "接口文档缺失[具体功能]相关接口，将在plan中标注依赖状态"
- ⚠️ "建议后续提供完整接口文档，以便迭代更新plan"

## 核心原则

### 1. 内容定位
- **只写方案逻辑，不写具体代码**
- 专注于"做什么"和"怎么设计"，不描述"怎么实现"
- 使用功能描述替代代码示例

### 2. 技术规范  
- **状态管理必须参考项目现有store.js格式**
- **严格使用接口文档中的真实接口**，禁止杜撰接口
- 缺失接口标注"待后端提供"，但保留功能设计

### 3. 文件组织规范
- **plan.md文件位置**：优先放置在提供的PRD文件相同目录下，保持文档就近原则
- **默认位置策略**：
  - 如果提供了PRD文件：将plan.md放在PRD文件的同级目录
  - 如果未指定PRD位置：将plan.md放在项目根目录的`doc/`目录下
- **文件命名规范**：使用项目或功能模块名称作为前缀，如`【功能名称】plan.md`
- **版本控制**：plan.md文件应纳入版本控制，便于团队协作和版本追踪

## 页面设计规范

### 1. 页面组织原则
- **按页面维度划分功能模块**：每个页面独立设计，清晰分工
- **完整的页面设计**：包含状态、布局、交互三个维度
- **层次化结构**：主页面 → 子页面 → 弹窗组件的层次组织
- **功能内聚**：相关功能在同一页面内组织，减少页面间依赖
- **组件分层原则**：
  - **页面专属组件**：只被单个页面使用的组件归属到该页面模块下
  - **通用组件**：可被多个页面复用的组件与页面平级设计
  - **组件职责明确**：避免页面组件承担通用功能

### 2. 页面设计三要素

#### A. 页面状态转换图（复杂页面需要）
使用Mermaid状态图描述页面状态变化：

**适用场景**：实时游戏、支付流程、复杂交互等状态驱动的应用

**设计要点**：
- 明确区分全局状态和用户状态
- 突出时间驱动 vs 用户驱动的状态转换
- 避免过度复杂的状态嵌套

**标准格式**：
```mermaid
stateDiagram-v2
    state "主流程状态" as MainFlow {
        状态A --> 状态B : 转换条件
        state 状态A {
            [*] --> 子状态1
            子状态1 --> 子状态2 : 用户操作
        }
    }
```

#### B. 页面布局描述
使用JSON格式描述页面层次结构：

**设计要点**：
- **必须对照原型图**：严格按照原型图的实际布局设计
- **层次化组织**：清晰的容器和组件层次关系
- **功能描述**：每个区域的功能说明，不涉及具体实现

**标准格式**：
```json
{
  "pageName": {
    "container1": {
      "subComponent1": "功能描述",
      "subComponent2": "功能描述"
    },
    "container2": "功能描述"
  }
}
```

#### C. 交互逻辑描述
按页面状态分组描述交互点的处理逻辑：

**标准格式**：
```
**状态阶段交互（全局状态：具体状态）**
- **交互组件名**:
  1. 触发条件/点击事件
  2. 调用接口或执行逻辑  
  3. 成功后的状态变更和UI反馈
  4. 失败时的错误处理和用户提示
```

### 3. 应用类型特殊考虑

#### 实时游戏/多用户应用
- **全局同步状态**：所有用户看到一致的游戏状态
- **用户个人状态**：每个用户独有的状态  
- **实时数据更新**：通过轮询或WebSocket保持状态同步

#### 交易/支付类应用
- **支付流程状态**：等待支付、支付中、支付成功、支付失败等
- **余额状态管理**：实时余额更新和校验逻辑
- **交易安全控制**：防重复提交、金额校验等

#### 记录/列表类应用
- **分页加载**：列表数据的加载和展示逻辑
- **状态筛选**：不同状态数据的筛选界面
- **空状态处理**：无数据时的界面设计

## 技术方案设计

### 状态管理设计
按照项目store.js格式，每个功能模块包含：
- **模块名称 (storeName)**
  - 管理的数据类型和范围
  - 调用的接口和参数
  - 数据处理逻辑描述

### 接口对接规范

#### 接口使用要求
- **只能使用接口文档中实际提供的接口**
- 接口路径、请求方式、参数必须与接口文档完全一致
- 缺失接口标注"待后端提供接口"，保留功能设计方案

#### 开发策略
- 优先实现使用已提供接口的功能
- 依赖待提供接口的功能先用模拟数据实现UI和交互
- 预留接口对接的代码结构

## AI代码生成任务清单规范

### 1. 任务分组原则

**标准分组结构**：
- **基础架构任务**：路由、状态管理、入口页面、环境配置等
- **核心页面任务**：按页面功能分组，每个页面包含其专属组件
  - 主页面任务（包含页面专属的弹窗、子组件等）
  - 记录页面任务（包含页面专属的列表组件、筛选组件等）
  - 其他功能页面任务（包含各自的专属组件）
- **通用组件任务**：可被多个页面复用的组件，与页面平级
  - 公共UI组件（按钮、输入框、加载器等）
  - 业务通用组件（用户头像、倒计时、动画组件等）
  - 基础设施组件（错误处理、网络请求等）

**组件归属原则**：
- **页面专属组件归属页面**：如结算弹窗归属主游戏页面
- **通用组件独立设计**：如倒计时组件、礼物展示组件等可复用组件单独分组
- **避免组件职责混乱**：明确区分页面特定功能和通用功能的边界

### 2. 任务描述规范

**任务命名格式**：
```
- [ ] 动词+功能目标（技术特性/依赖状态）
```

**依赖状态标注**：
- **已提供接口**：直接描述功能实现
- **待提供接口**：添加"（依赖待提供的XX接口）"
- **模拟数据处理**：标注"临时使用模拟数据，待接口提供后对接真实数据"

### 3. 任务优先级设计
1. **P0-基础架构**：路由、状态管理等基础设施
2. **P1-核心功能**：使用已提供接口的主要功能页面
3. **P2-扩展功能**：依赖待提供接口的功能，先用模拟数据实现
4. **P3-优化组件**：通用组件、错误处理、性能优化等

### 4. 任务清单模板

```markdown
## AI代码生成任务清单

### 基础架构任务
- [ ] 生成路由配置文件，添加活动路由
- [ ] 生成Vuex store文件，按照home/store.js格式实现
- [ ] 生成主入口页面框架
- [ ] 配置活动相关的环境参数

### 核心页面任务组
#### [页面名称]页面任务
- [ ] 生成页面结构和样式
- [ ] 实现核心功能（具体功能描述）
- [ ] 实现页面专属组件（弹窗、子组件等）
- [ ] 实现状态管理逻辑
- [ ] 集成API接口调用

#### [其他页面名称]页面任务（依赖待提供接口）
- [ ] 生成页面结构和样式
- [ ] 实现页面专属组件
- [ ] 实现UI和交互逻辑
- [ ] 临时使用模拟数据，待接口提供后对接真实数据

### 通用组件任务
- [ ] 实现公共UI组件（按钮、输入框等）
- [ ] 实现业务通用组件（倒计时、动画等）
- [ ] 实现基础设施组件（错误处理、网络请求等）
```

## 设计质量检查

### 页面布局检查
1. ✅ 对照原型图检查每个元素位置
2. ✅ 确认所有元素在原型图中存在  
3. ✅ 验证容器和组件的层次关系
4. ✅ 检查相关功能元素的视觉距离

### 任务完整性检查
1. ✅ 所有页面功能都有对应任务
2. ✅ 组件依赖关系明确
3. ✅ 接口使用完全覆盖
4. ✅ 依赖状态标注清楚

### 常见错误避免
❌ **布局错误**：基于经验而非原型图进行设计  
❌ **接口错误**：杜撰不存在的接口  
❌ **组件混乱**：页面组件和通用组件职责不清  
❌ **状态复杂**：过度使用复杂的状态嵌套

---

遵循以上规范，确保plan文档专注于设计方案，严格使用真实接口，准确对照原型图设计，合理组织组件架构，为AI代码生成提供清晰指导。
❌ 不包含代码实现细节
❌ 不设置里程碑和截止日期

遵循以上规范，确保plan文档专注于设计方案，严格使用真实接口，准确对照原型图进行布局设计，深入分析业务逻辑复杂性，完整设计状态转换机制，生成简洁清晰的流程图，为后续AI代码生成提供准确指导。