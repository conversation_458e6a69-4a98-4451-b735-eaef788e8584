---
description: 
globs: 
alwaysApply: true
---
 # Sketch JSON解析规则

## 当从sketch导出的JSON文件（如artboard.json）生成组件时，请遵循以下规则：

1. 检查并处理所有的 "images" 字段，无论它们的命名规则如何，都应当在生成的代码中引用
2. 对于命名以 "shapecombine" 开头的图片，应特别注意，它们通常是形状组合，应在对应层结构中实现
3. 每次生成组件前，先提取JSON中所有图片引用，确保所有图片在最终代码中都有对应使用
4. 对于每个layer对象中的isExportableShape或isExportableGroup为true的元素，必须检查其images属性
5. 如果一个图形有images属性但没有具体形状定义，应当直接使用background-image实现

## 图片处理原则

1. 对于JSON中的图片引用（"images"字段），始终保持原样使用，不要尝试解析图片内容
2. 不要将图片中的文字提取为单独的HTML/CSS元素，保持图片的完整性
3. 图片就是图片，代码就是代码，二者不应混合处理
4. 如果一个元素在JSON中定义为图片，那它在代码中也应该只是一个图片，不需要额外的文字或元素

## 像素级还原原则

1. 所有元素的位置、尺寸、间距必须与JSON中的坐标和数值完全一致
2. 不要简化或统一化设计中的不规则间距，保持原始设计的精确性
3. 对于设计中元素的特殊分布(如不等间距的进度点)，必须精确复制而非"规范化" 