# aquaman-ai-test

【海王1.0】AI研发流程测试

## 资源

* [开发流程](https://doc.yupaopao.com/pages/viewpage.action?pageId=11438463)

* [全局脚本项目](http://git.yupaopao.com/terminal/fe/infrastructure/global)

* [通用组件库项目](http://git.yupaopao.com/terminal/fe/infrastructure/lego)
* [通用组件库文档](http://test-h5.hibixin.com/bixin/lego/index)

* [H5产品组件库项目](http://git.yupaopao.com/terminal/fe/infrastructure/dragon)
* [H5产品组件库文档](http://test-h5.hibixin.com/bixin/dragon/index)

* [JS Bridge 列表](https://test-h5.hibixin.com/bixin/bridge/index#/)， [旧列表](https://doc.yupaopao.com/pages/viewpage.action?pageId=11437012)
* [JS Bridge 使用文档](http://git.yupaopao.com/terminal/fe/infrastructure/global/blob/master/HYBRID.md)
* [Native Schema 列表](https://mobile.yupaopao.com/scheme/list.html)， [旧列表](https://doc.yupaopao.com/pages/viewpage.action?pageId=12885432)

* [埋点 project 列表](https://app.yupaopao.com/#/statlist)
* [埋点 source 列表](https://doc.yupaopao.com/pages/viewpage.action?pageId=15961111)


## 快速上手

* 设置全局环境

```bash
# 使用Watt发布可跳过
npm i isaac-cli -g --registry=https://repo.yupaopao.com/artifactory/api/npm/npm-repo/
```

* 安装依赖

```bash
npm i
```

* 启动本地开发服务

```bash
npm run dev
```
- 咨询：sunbinbin <<EMAIL>>
