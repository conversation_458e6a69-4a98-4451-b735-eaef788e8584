<template>
  <div
    id="app"
    :class="[
      $style.app,
      {
        [$style[`is-half-full-page`]]: isHalfFullPage,
        [$style[`is-small-size`]]: isSmallSize,
        [$style[`is-pc-confession`]]: isPcConfession,
      },
    ]"
  >
    <!--
      服务端会缓存 html，navbar 的高度是由 userAgent 决定的，所以如果缓存开了就不能显示 navbar

      isNotSSR 是 @starter/mixins/ssr 中生成的变量
    -->
    <NavigationBar v-bind="globalNavbarProps" @info="onNavbarInfo" :homePath="homePath" :shareData="shareData" />

    <div id="half_mask" :class="$style.mask" v-if="isHalfFullPage" @click="$utils.closePage"></div>

    <keep-alive>
      <router-view :class="$style.view" v-if="$route.meta.keepAlive" @updateNavbar="updateNavbar" v-bind="routerProps" />
    </keep-alive>
    <router-view :class="$style.view" v-if="!$route.meta.keepAlive" @updateNavbar="updateNavbar" v-bind="routerProps" />

    <!--
      注意：不要删除 openInBrowser 这个属性

      支持分享的页面都需要使用此组件，微博打开时会有一个用浏览器打开的引导
    -->
    <download openInBrowser />
  </div>
</template>

<script>
import { NavigationBar, Download } from '@fe/lego'
import createApp from '@starter/mixins/createApp'
import halfMixin from '@starter/mixins/half-mixin'

/**
 * 下面是默认的 navbar 属性
 *
 * 需要覆盖此默认的 navbar 属性可以在下面两个地方中的一个配置即可
 * 1. router.js 中配置 meta.navbarProps
 * 2. 页面组件的 data 中配置 navbarProps（通过这种方式也可以在页面中动态更新 navbarProps）
 */
const defaultNavbarProps = {
  /** 是否显示 navbar */
  hidden: false,

  /** 暗黑风格图标 */
  // imageTheme: 'gray',

  /** 文字颜色 */
  // color: '#333',

  /** navbar 标题 */
  title: '语音活动模板',

  /** navbar 标题 透明 */
  titleOpacity: true,

  /** navbar 背景色，也可以指定成渐变色 */
  backgroundColor: '#42b983',

  /** 表示 navbar 变成不透明状态需要移动的最大距离 */
  maxScrollDistance: 200,

  /** 为 true 时表示 navbar 不占空间，意味着底部内容会穿透到 navbar 下面；否则底部内容不会穿透到 navbar 下面 */
  fullScreen: true,
}

export default {
  components: {
    NavigationBar, Download,
  },
  mixins: [
    createApp(defaultNavbarProps),
    halfMixin,
  ],
  data () {
    const appInfo = (this.$mc.detail && this.$mc.detail.appInfo) || {}

    return {
      navbarHeight: appInfo.navbarHeight || 44,
      statusHeight: appInfo.statusHeight || 44,
      homePath: '/',
      // FIXME: 根据不同业务设置分享数据\微信二次分享数据（标题、描述、链接及分享icon）
      shareData: {
        title: '标题标题标题标题标题标题',
        desc: '描述描述描述描述描述描述',
        link: location.href,
        image: 'https://p6.hellobixin.com/bx-user/2ae20a5d73ba439498aa53e9794f3521.png',
        yppImage: 'https://p6.hellobixin.com/bx-user/2ae20a5d73ba439498aa53e9794f3521.png',
      },
    }
  },
  computed: {
    halfContentHeightStyle () {
      if (this.isHalfFullPage) return (this.isSmallSize ? 133.4 : 162.4) + 'vw'
      return '100vh'
    },
    paddingTop ({ navbarHeight, statusHeight }) {
      return navbarHeight + statusHeight
    },
    paddingTopStyle ({ paddingTop }) {
      return {
        paddingTop: paddingTop + 'px',
      }
    },
    containerHeight ({ paddingTop }) {
      return this.$isServer ? 1000 : document.documentElement.clientHeight - paddingTop
    },

    routerProps ({ paddingTop, paddingTopStyle, containerHeight }) {
      return { paddingTop, paddingTopStyle, containerHeight }
    },
  },
  created () {
    // 设置1级路由（点击可关闭容器）
    this.homePath = this.$route.path

    // 去除原生的 navbar，并将状态栏隐藏
    if (this.$bridge.isInYpp) {
      this.$bridge.setNavbarType({ type: 'hide', statusBarType: 'light' })
    } else {
      // 设置微信二次分享
      this.$utils.wxShare({
        shareConfig: this.shareData,
        subject: 'yuer',
        wxTestAppId: 'wx29eccbc880fc6965',
        wxTestAppSecret: '656553790f923f13e2b46debfdb5a8ba',
      })
    }

    if (this.$bridge.app.isYppPC && !this.$isServer) {
      document.documentElement.classList.add('gScrollStyleDeep')
    }

    // 注入rainbow上报（配置了activityId才启用）
    const activityId = this.$config.activityId
    if (activityId && window.rainbow) {
      window.rainbow.setConfig({
        appName: 'yuer',
        // 需要唤起的scheme
        scheme: `yuer://webpage/push?yppSupportPageComplete=1&yppHideNavBar=1&url=${location.href}`,
        bar: true,
        mask: !__LOCAL__,
        activityData: {
          type: 'activity',
          // FIXME:根据不同业务配置activityId及设置activityName
          activityId,
          activityName: '标题标题',
        },
      }).then(() => {
      // 来源数据上报
        window.rainbow.report({ eventType: 'pv' })
      })
    }
  },
  mounted () {
    /**
     * 调用h5通知客户端关闭loading桥
     * 延迟是为了关闭loading进入h5不闪白（50ms需要根据每个项目的复杂程度动态变化）
     * 前缀的拼接方式(h5通知客户端关闭loading)：yuer://webpage/push?yppSupportPageComplete=1&yppHideNavBar=1&url=
     */
    if (YPP.app.isYppMC) {
      setTimeout(() => {
        this.$bridge.call('page_complete')
      }, 50)
    }
  },
  methods: {
    /**
     * 通过 navbar 组件获取当前设备的 navbar 高度和状态栏高度
     * @param {{navbarHeight: number, statusHeight: number}} info
     */
    onNavbarInfo (info) {
      this.navbarHeight = info.navbarHeight
      this.statusHeight = info.statusHeight
    },

    /**
     * 更新 navbar 的配置
     */
    updateNavbar (newProps) {
      // createApp mixin 中的方法
      this.updateGlobalNavbar(newProps)
    },
  },
}
</script>

<style lang="scss" module>
@import "common/styles/_app.scss";
</style>
