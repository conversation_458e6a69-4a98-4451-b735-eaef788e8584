// 合并store至原始结构
const formatStore = storeConfig => {
  const actions = {}
  const mutations = {}
  let initState = {}
  Object.keys(storeConfig).forEach(key => {
    if (storeConfig[key].default && typeof storeConfig[key].default === 'object') {
      initState = { ...initState, ...storeConfig[key].default }
    }
    if (storeConfig[key].action) {
      actions[key] = async (sugar, params) => {
        // 执行action
        const res = await storeConfig[key].action(sugar, params)
        // 提交mutation
        if (storeConfig[key].mutation) {
          sugar.commit(key, res)
        }
        // 触发其他事件
        storeConfig[key].emit && storeConfig[key].emit(sugar, res)
        return res
      }
    }
    if (storeConfig[key].mutation) {
      mutations[key] = storeConfig[key].mutation
    }
  })

  return {
    namespaced: true,
    actions,
    mutations,
    state: initState,
  }
}

// 自动提取并合并pages下面的store文件
const extractStore = () => {
  const modules = {}
  const requireStores = require.context('./pages', true, /^\.\/(\w*\/)+store\.js$/)
  requireStores.keys().forEach(fileName => {
    const storeConfig = requireStores(fileName).default
    // 提取module名称
    const moduleName = fileName.replace(/^\.\//, '').replace(/\/store\.\w+$/, '')
    modules[moduleName] = formatStore(storeConfig)
  })

  return modules
}

/** @type {import('vuex').StoreOptions} */
const STORE_OPTIONS = {
  modules: {
    // 自动引入
    ...extractStore(),
    // 可选的手动引入
    common: formatStore(require('@starter/utils/common-fetch').default),
  },
  // 持久化开关
  persisted: true,
}

export default STORE_OPTIONS
