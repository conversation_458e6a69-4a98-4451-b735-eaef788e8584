<template>
  <div class="gTextCenter" :class="$style.container">
    <ul :class="$style.examples">
      <li><a href="https://test-h5.hibixin.com/bixin/lego/index" target="_brank">组件库文档</a></li>
      <li><a href="https://test-h5.hibixin.com/bixin/bridge/index" target="_brank">Bridge API</a></li>
      <li><a href="https://doc.yupaopao.com/pages/viewpage.action?pageId=11437012" target="_brank">JS Bridge 文档</a></li>
      <li><a href="https://doc.yupaopao.com/pages/viewpage.action?pageId=12885432" target="_brank">Native Schema 列表</a></li>
      <li><a href="https://doc.yupaopao.com/pages/viewpage.action?pageId=15965624" target="_brank">埋点 project 列表</a></li>
      <li><a href="https://doc.yupaopao.com/pages/viewpage.action?pageId=15961111" target="_brank">埋点 source 列表</a></li>
    </ul>
    <p :class="$style.help">上面所有文档都可以在根目录下的 README.md 文件中查看</p>
    <button @click="randomUpdateNavbar" style="margin-top: 2em" data-track="updateNavbar">随机更新 navbar 背景</button>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  // 获取 app.vue 传进来的 paddingTop 和 containerHeight
  props: ['paddingTop', 'containerHeight'],
  data () {
    return {
      // 可以配置 navbar 属性（提供给 @starter/mixins/navbar 使用）
      navbarProps: {
        maxScrollDistance: 0,
      },

      // 可以配置需要预加载的图片或svga文件（提供给 @starter/mixins/preload 使用）
      preloadResources: [
        'https://yphoto.eryufm.cn/FnMIIbE-slAlFzA7h1L9aEnQmY1u?imageView2/1/w/459/h/837',
        'https://yphoto.eryufm.cn/FgcuQ4RsqqvAsFGA1bi6q-O9Zy8e',
      ],
    }
  },
  methods: {
    ...mapActions('home', ['setFlag']),
    ...mapActions('common', ['getStageList']),
    randomUpdateNavbar () {
      const r = () => Math.round(Math.random() * 255)
      this.navbarProps = { backgroundColor: `rgb(${r()}, ${r()}, ${r()})` }
      window.autotrack.track('更新背景按钮')
    },
  },
  mounted () {
    this.setFlag()
    this.getStageList({ sceneIds: [1] }).then(res => {
      console.log('赛段配置 ==> ', res)
    })
  },
}
</script>

<style lang="scss" module>
.container {
}
.examples {
  font-size: 14px;
  li {
    margin: 30px;
  }
  a {
    text-align: left;
    display: block;
    text-decoration: none;
    color: #42b983;
  }
  a[target]::after {
    display: inline;
    content: "（跳新页）";
    color: #ccc;
    font-size: 12px;
  }
}
.help {
  color: #999;
  font-style: italic;
  padding: 0 15px;
  font-size: 12px;
}
</style>
