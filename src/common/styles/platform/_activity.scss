body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  -webkit-overflow-scrolling:touch;  // 滑动流畅

  -moz-user-select: none;   // 防止刮蓝
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  
  -webkit-touch-callout: none;// 禁止iOS13图片放大

  -webkit-tap-highlight-color:rgba(0,0,0,0); // 去掉点击阴影
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}