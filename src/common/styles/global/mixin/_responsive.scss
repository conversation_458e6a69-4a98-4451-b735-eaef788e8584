@mixin from($device) {
  @media screen and (min-width: $device) {
    @content;
  }
}

@mixin until($device) {
  @media screen and (max-width: $device - 1px) {
    @content;
  }
}

@mixin retina($ratio) {
  @media (-webkit-min-device-pixel-ratio: #{$ratio}), (min-resolution: #{$ratio}dppx) {
    @content;
  }
}

@mixin retina2() {
  @include retina(2) {
    @content;
  }
}

@mixin retina3() {
  @include retina(3) {
    @content;
  }
}
