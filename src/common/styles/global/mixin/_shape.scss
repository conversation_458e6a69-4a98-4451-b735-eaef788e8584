@mixin rect($w-h-lh, $display: false, $radius: false) {
  $width: nth($w-h-lh, 1);
  $height: if(length($w-h-lh) >= 2, nth($w-h-lh, 2), false);
  $line-height: if(length($w-h-lh) >= 3, nth($w-h-lh, 3), false);

  @if $display and $display != '' {
    display: $display;
  }

  @if $width {
    width: $width;
  }

  @if $height {
    height: $height;
  }

  @if $line-height == true {
    line-height: nth($w-h-lh, 2);
  } @else if $line-height {
    line-height: $line-height;
  }

  @if $radius == true {
    @if $height {
      border-radius: $height / 2
    } @else {
      border-radius: 50%;
    }
  } @else if $radius {
    border-radius: $radius;
  }
}

@mixin square($size-lh, $display: false) {
  $size: nth($size-lh, 1);
  $line-height: if(length($size-lh) >= 2, nth($size-lh, 2), false);
  @include rect($size $size $line-height, $display, false);
}

@mixin circle($size-lh, $display: false, $radius: true) {
  $size: nth($size-lh, 1);
  $line-height: if(length($size-lh) >= 2, nth($size-lh, 2), false);
  @include rect($size $size $line-height, $display, $radius);
}

/*
  Creates a visual triangle. Mixin takes ($size, $color, $direction)

  The $size argument can take one or two values—width height.

  The $color argument can take one or two values—foreground-color background-color.

  $direction: up, down, left, right, up-right, up-left, down-right, down-left

  @include triangle(12px, gray, down);
  @include triangle(12px 6px, gray lavender, up-left);
*/
@mixin triangle ($size, $color, $direction) {
  height: 0;
  width: 0;

  $width: nth($size, 1);
  $height: nth($size, length($size));

  // 背景色无法覆盖，前景色可以覆盖
  $foreground-color: nth($color, 1);
  $background-color: if(length($color) == 2, nth($color, 2), transparent);
  $background-color: $background-color;

  @if ($direction == up) or ($direction == down) or ($direction == right) or ($direction == left) {

    $width: $width / 2;
    $height: if(length($size) > 1, $height, $height/2);

    @if $direction == up {
      border-left: $width solid $background-color;
      border-right: $width solid $background-color;
      border-bottom: $height solid $foreground-color;
      border-top: none;

    } @else if $direction == right {
      border-top: $width solid $background-color;
      border-bottom: $width solid $background-color;
      border-left: $height solid $foreground-color;
      border-right: none;

    } @else if $direction == down {
      border-left: $width solid $background-color;
      border-right: $width solid $background-color;
      border-top: $height solid $foreground-color;
      border-bottom: none;

    } @else if $direction == left {
      border-top: $width solid $background-color;
      border-bottom: $width solid $background-color;
      border-right: $height solid $foreground-color;
      border-left: none;
    }
  }

  @else if ($direction == up-right) or ($direction == up-left) {
    border-top: $height solid $foreground-color;

    @if $direction == up-right {
      border-left:  $width solid $background-color;

    } @else if $direction == up-left {
      border-right: $width solid $background-color;
    }
  }

  @else if ($direction == down-right) or ($direction == down-left) {
    border-bottom: $height solid $foreground-color;

    @if $direction == down-right {
      border-left:  $width solid $background-color;

    } @else if $direction == down-left {
      border-right: $width solid $background-color;
    }
  }

  @else if ($direction == inset-up) {
    border-width: $height $width;
    border-style: solid;
    border-color: $background-color $background-color $foreground-color;
  }

  @else if ($direction == inset-down) {
    border-width: $height $width;
    border-style: solid;
    border-color: $foreground-color $background-color $background-color;
  }

  @else if ($direction == inset-right) {
    border-width: $width $height;
    border-style: solid;
    border-color: $background-color $background-color $background-color $foreground-color;
  }

  @else if ($direction == inset-left) {
    border-width: $width $height;
    border-style: solid;
    border-color: $background-color $foreground-color $background-color $background-color;
  }
}
