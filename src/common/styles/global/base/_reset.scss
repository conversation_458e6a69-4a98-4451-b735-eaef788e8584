$rootBackgroundColor: white !default;
$rootFontFamily: "Alibaba-PuHuiTi-R", -apple-system, "PingFangSC-Regular", "Helvetica Neue", STHeiti, "Microsoft Yahei", <PERSON><PERSON><PERSON>, <PERSON><PERSON>,
  sans-serif;
$codeFontFamily: Courier, "Courier New", monospace;
$rootFontSize: 14px !default;
$baseColor: #333 !default;
$placeholderColor: #ccc !default;

article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  vertical-align: baseline;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
menu,
nav,
section {
  display: block;
}

// // HTML5 媒体文件跟 img 保持一致
audio,
canvas,
video {
  display: inline-block;
}

// *, *::before, *::after {
//   // 根据个人习惯，使用 common.scss 中 gBoardBox 样式名
//   // box-sizing: border-box; // IE 6/7 不支持

//   // 图片默认不重复
//   // background-repeat: no-repeat;
// }

html {
  text-rendering: optimizelegibility;
  text-size-adjust: 100%;

  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);

  // In addition in IE10, also add this CSS rule to remove the ghost click delay
  // http://ariatemplates.com/blog/2014/05/ghost-clicks-in-mobile-browsers/
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  //-moz-osx-font-smoothing: grayscale;
  //-webkit-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html,
body {
  background-color: $rootBackgroundColor;
  font-family: $rootFontFamily;
  font-size: $rootFontSize;
  // overflow-x: hidden; // 造成打印页中 x 轴被截断；也会造成 PC 端自定义的 Select 组件的下拉框被遮罩
  // 此属性会影响 position: fixed; bottom: 0; 的元素
  // 效果是：当拖到底后，再拖动的话，底部的 fixed 元素会逐渐被遮挡了
  // -webkit-overflow-scrolling: touch;
}

button,
input,
select,
textarea,
body {
  font-family: $rootFontFamily;
  color: $baseColor;
}

input::-ms-clear {
  display: none;
}

input[type="password"]::-ms-reveal {
  display: none;
}

// 表单元素不会继承父元素的字体
button,
input,
select,
textarea {
  font-family: $rootFontFamily;
}

// 代码片段
pre,
code {
  font-family: $codeFontFamily;
  white-space: pre-wrap;
  word-wrap: break-word;
}

html,
body,
ul,
li,
ol,
dl,
dd,
p {
  margin: 0;
  padding: 0;
}

ul,
ol {
  list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  // font-size: inherit; // antd 不需要此值
  font-weight: normal;
}

ins,
a {
  text-decoration: none;
  cursor: pointer;
}

a {
  outline: none !important;
  color: inherit;
}

img {
  -webkit-user-drag: none;
  border: 0;
  -ms-interpolation-mode: bicubic;
}

svg {
  display: inline-block;
}
svg:not(:root) {
  overflow: hidden;
}

form {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
  border: 0;
}

button,
input,
select,
textarea {
  margin: 0;
  font-size: 100%;
  vertical-align: baseline;
}

button,
input {
  line-height: normal;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
}

input[type="checkbox"],
input[type="radio"] {
  padding: 0;
}

input[type="search"] {
  -webkit-appearance: textfield;
}

input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

input,
textarea {
  // Firefox
  &::-moz-placeholder {
    color: $placeholderColor;
    opacity: 1; // Override Firefox's unusual default opacity; see https://github.com/twbs/bootstrap/pull/11526
  }
  &:-ms-input-placeholder {
    color: $placeholderColor;
  } // Internet Explorer 10+
  &::-webkit-input-placeholder {
    color: $placeholderColor;
  } // Safari and Chrome
}

// 去掉 moz 按钮边上的光环
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}

textarea {
  overflow: auto;
  vertical-align: top;
}

.reset {
  margin: 0;
  padding: 0;
  border: 0;
  background: transparent;
  text-decoration: none;
  width: auto;
  height: auto;
  vertical-align: baseline;
  box-sizing: content-box;
  position: static;
  transition: none;
  border-collapse: collapse;
  font: normal normal normal $rootFontSize $rootFontFamily;
  color: $baseColor;
  text-align: left;
  white-space: nowrap;
  cursor: auto;
  float: none;
}
