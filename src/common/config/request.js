// @fe/request 的配置项，提供给 @starter 项目使用
import Activity from './activity'

/**
 * @typedef {ConstructorParameters<typeof import('@fe/request').default>[0]} ConstructorOptions
 */

/**
 * new Request(options) 时传入的参数
 * @type {ConstructorOptions}
 */
const options = {}
if (Activity.activityCode) {
  options.headers = {
    ...options.headers,
    'X-Activity-Code': Activity.activityCode,
  }
}

// 服务端环境SSR才会携带mock-key，客户端不携带
if (typeof global === 'object' && global && global.mockKey) {
  options.headers = {
    ...options.headers,
    'Mock-Key': (typeof global === 'object' && global && global.mockKey),
  }
}

/**
 * 请求参数拦截
 * @param {ConstructorOptions} options
 * @returns {ConstructorOptions}
 */
function requestInterceptor (options) {
  return options
}

/**
 * 返回的内容拦截
 * @param {any} res
 * @param {ConstructorOptions} options
 * @returns {any}
 */
function responseInterceptor (res, options) {
  return res
}

export default {
  // successCodes: ['8000'] // 可以自定义成功的状态码
  options,
  requestInterceptor,
  responseInterceptor,
}
