# 【谁是海王】玩法文档

# 一、基础信息

活动平台：比心app&鱼耳语音app&语音pc

需求背景：丰富语音礼物玩法，增加厅内一起参与的互动玩法类型，提升付费粘性，提高arppu帮助业务实现营收目标。

类型概述：礼物类玩法活动，活动外皮受赏者无收益，用户平进平出无增益减益，仅为互动娱乐工具。

参与身份：不限制身份及等级，但需要走实名认证逻辑，未实名用户引导实名，实名用户需要拦截未成年人/自己/同实名账号，触发校验：打赏活动外皮礼物；

玩法开放时间：24h

资金逻辑：面板外皮礼物打赏、活动页面购买活动道具，资金均流入活动账户

# 二、玩法逻辑

## 2.1玩法概述

1.  全服用户一起参与，每个用户一艘潜艇，一起出发；
    
2.  页面存在一艘潜艇，用户使用活动道具【珍珠】来参与，投入珍珠后等待潜艇下潜；
    
3.  潜艇下潜之前，系统会随机一个深度，作为最大深度；
    
4.  下潜时，用户随时可以选择返回，用户选择返回时的深度越深，获得的奖励就越好；但如果深度超过最大深度，则失败；
    
5.  全服共同参与，共享下潜时间、下潜深度；
    
6.  每一轮全服参与人数（成功人数）>=2人时，下潜深度数值最高的用户获得“海王”，这里仅展示特殊
    

## 2.2比心币逻辑

触发场景：比心APP

1）面板外皮礼物

现状：用户在面板打赏礼物时，如果钻石不足但比心币充足，会弹出使用比心币的弹窗。此时点击确认会使用比心币付款。且勾选“统一使用并下单”，在下次触发时会自动使用。但上述规则不支持本次的面板外派礼物。

![image.png](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/res/8K4nyepJgaJbonLb/img/280aed7f-b25d-4739-a5db-fb96c04cf696.png)

增加逻辑：需要支持本活动的面板外皮礼物；

2）活动页面购买道具

触发条件：用户钻石不足但比心币充足，弹出弹窗（具体页面详见交互）。

逻辑：用户可以使用比心币付款，同时勾选选项后，下次触发将会直接使用比心币付费（仅限钻石不足，比心币充足的情况）

特殊：

1）用户勾选后并确认使用后，下次满足条件将自动使用

2）用户未勾选，则下次触发时依然弹出弹窗

## 2.3道具来源

获取活动道具：每赠送1个【礼物名称】可获得活动道具【珍珠】\*1，价值1000钻/个，

用户可在礼物面板直接赠送外皮礼物——【礼物名称】，价值1000钻/个（后续可能调整，以礼物仓库配置钻石价值为准扣除用户钻石），此外皮礼物仅扣除用户钻石，不增加打赏者经验、不增加受赏者魅力值、不增加贡献榜/魅力榜/人气榜等常驻榜单、不增加平台内其他活动钻石任务进度，不参与受赏者公会分成等场景，外皮礼物不上礼物墙；

用户在活动中获得的礼物进入背包，且打赏这些礼物走正常钻石礼物打赏逻辑，增加打赏者经验、增加受赏者魅力值、增加榜单分值、增加平台内其他活动钻石任务进度，参与公会分成场景（与周星礼物/月限礼物等普通面板礼物一致），需要上礼物墙，支持开出来的礼物起飞机横幅；

面板赠送【礼物名称】数量选项不需要特殊处理，正常触发飞机横幅；

## 2.4参与数量

用户通过礼物面板【礼物名称】礼物横幅入口进入页面；

用户可在活动页面参与，每次消耗若干【珍珠】。用户只能选择不同档位的数量，不可以自定义；

珍珠价格标定：1个珍珠=1000钻=10元

下文中的档位支持配置，包括增删、编辑文案和对应的数量

| 档位 | 数量 |
| --- | --- |
| 入门 | 1 |
| 普通 | 10 |
| 优异 | 50 |
| 卓越 | 300 |
| 超凡 | 600 |
| 传奇 | 1000 |

## 2.5参数配置

需要配置的基础信息

1.  最大深度：s（默认配置10000米）
    
2.  每轮的等待时间：t1（默认配置30s）
    
3.  潜艇最大下潜时间：t2（这里指的是下潜最大深度所需的时间，默认配置25s）
    
4.  结算时间：t3（默认配置5s）
    
5.  即一轮的时间为t1+t2+t3；
    

| 等待时间 | 下潜时间 |  | 结算时间 |
| --- | --- | --- | --- |
| t1（30s） | t2（25s） |  | t3（5s） |
|  | 实际下潜时间<br>这里指的是达到本次随机的最大深度的时间，所有用户也是在本时间结束后统一发奖 | 剩余时间 |
|  |  | 实际结算时间 |  |

下潜速度：

这里潜艇不是匀速的，随着深度增加，速度也会增加，需要商定速度的来源

建议：

~~1）深度每增加x米，则速度提升y%；然后通过给定的配置时间t1，来决定初始速度；~~

2）不同区间的配置速度不同，单个区间匀速。具体效果按照验收时调整

每一轮开启后：

1.  系统随机一个深度：x米（这里的概率配置在下文）
    
2.  随机深度跑完的时间为t4（这里是计算得出）
    
3.  下潜结束后，本轮的结算时间为t2-t4+t3
    

## 2.6结算配置

结算时机：下潜到最大深度时（即本轮系统随机出的米数），所有用户一起结算

结算时，分为两种情况

1.  用户超出最大深度，则获得兜底奖励
    
2.  用户未超出最大深度，则按照深度的级别获取奖励
    

深度概率&级别配置

1.  深度支持增删和修改区间，下文为默认配置
    
2.  概率的应用，先按照概率随机深度区间，然后区间内的数字再次均等随机；距离：15%的概率随机到了1000-1999，然后这个区间有1000m，则每一个数字礼物1400的概率就是1/1000；
    

| 距离随机 |  | 概率 |
| --- | --- | --- |
| 下限 | 上限（开区间） |
| 0 | 1000 | 0 |
| 1000 | 1999 | 15% |
| 2000 | 2999 | 20% |
| 3000 | 4999 | 20% |
| 5000 | 6999 | 20% |
| 7000 | 8999 | 15% |
| 9000 | 9500 | 10% |
| 9500 | 10000 | 0 |

| 深度 |  | 奖励等级 |
| --- | --- | --- |
| \- |  | 兜底 |
| 0 | 999 | 0 |
| 1000 |  | 1 |
| 2000 |  | 2 |
| 3000 |  | 3 |
| 5000 |  | 4 |
| 7000 |  | 5 |
| 9000 |  | 6 |

级别应用

1.  这里的档位/级别会随着上文的配置而发生变化
    
2.  奖励内容的配置为背包礼物，用户获得后进入背包；
    
3.  背包礼物的打赏走正常打赏逻辑
    

| 档位/级别 | 兜底 | 1 | 2 | 3 | 4 | 5 | 6 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 入门 | 奖励内容 | 1802\*10 | 1805\*10 | 1803\*10 | 1905\*3 |  |  |
| 普通 |  | 1802\*10 | 1805\*10 | 1803\*10 | 1905\*3 | 1903\*1 | 1902\*1 |
| 优异 |  | 1802\*10 | 1805\*10 | 1803\*10 | 1905\*3 | 1903\*1 | 1902\*1 |
| 卓越 |  | 1802\*10 | 1805\*10 | 1803\*10 | 1905\*3 | 1903\*1 | 1902\*1 |
| 超凡 |  | 1802\*10 | 1805\*10 | 1803\*10 | 1905\*3 | 1903\*1 | 1902\*1 |
| 传奇<br>1000元 |  | 1802\*10 | 1805\*10 | 1803\*10 | 1905\*3 | 1903\*1 | 1902\*1 |

## 2.5消息播报

概述：用户在活动中的部分结果会进行播报展示

1）海王

1.  触发：用户获得了海王，则聊天室播放消息
    
2.  形式：聊天室房间信息
    
3.  文案：恭喜【用户昵称】在谁是海王活动中下潜最深，获得海王称号。
    
4.  其中入门、普通、优异、卓越为本聊天室播报，超凡、史诗、传奇为全聊天室播报（限制模板播客、情感、交友、歌厅、派单）；
    
5.  相同深度，后到达的覆盖先的；
    
6.  ~~备注：后续会增加聊天室房间消息；~~
    

2）获得礼物

1.  触发：用户获得的礼物，存在单个礼物价值大于x
    
2.  形式：活动飘屏
    
3.  文案：恭喜【用户昵称】在谁是海王活动中获得了【礼物名称】；
    
4.  依据最大礼物判断全服or频道，50000-99999钻的频道飘屏，100000钻及以上的全服飘屏；
    
5.  释义：
    
    1.  按照本轮获得的最大礼物进行判定
        
    2.  如果获得的礼物最高价值50000-99999钻，则频道飘屏，展示所有50000-99999钻的礼物
        
    3.  如果获得的礼物最高价值大于等于100000钻，则全服飘屏，展示所有大于100000钻的礼物
        
6.  按照单个礼物的价值从高到低排序。且展示数量，但数量为1的时候不展示；
    
7.  举例：恭喜【用户昵称】在谁是海王活动中获得了【礼物名称】\*2、【礼物名称】；（价格支持配置，不频繁，尽量不做运营配置）
    

## 2.6榜单逻辑

排序数值：下潜的总深度，但只计算成功的深度；

榜单周期：周榜，奖励自动发放

榜单种类：

1.  海王榜：按照用户本周下潜的总深度进行排名；
    
2.  海域榜：按照本房间，所有参与用户下潜的总深度之和进行排名；
    

奖励配置：只有海王榜有奖励

| 名次 | 奖励（装扮） |
| --- | --- |
| 1 |  |
| 2 |  |
| 3 |  |
| 4-10 |  |

~~榜单隐身：隐身状态时用户头像及昵称显示为神秘人~~

消息发送：

1.  榜单：海王榜
    
2.  发送消息端：按照用户最后一次参与【谁是海王】活动的app为准
    
3.  内容：
    
    1.  标题：【谁是海王】周榜奖励发放
        
    2.  文案：恭喜你在上周【海王榜】中获得第x名，获得【奖励内容】，具体可在【装扮中心-我的装扮】中查看。
        

## 2.7账单展示

1.  账单显示
    

打赏者钱包明细如下：

房间礼物面板——

【礼物名称】\*数量-谁是海王活动礼物                           扣除钻石数

受赏者昵称 受赏者ID

打赏时间

参与时间

受赏者收入明细如下：（如果没有更好）

房间礼物面板——

【礼物名称】\*数量-谁是海王活动礼物       -来自用户昵称

受赏时间                          不增加魅力值

## 2.8直送逻辑

用户在结算获得礼物时，弹窗上可以选择进入背包or直接送给主播

用户点击“选打赏对象”-弹出选人弹窗（这里选择要送给的目标）-再次确认后，本轮获得的礼物将直接送给目标；

详细逻辑参照[《活动页面直送逻辑梳理》](https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89gjAQR9FrLxg3qGV3kdP0wQ)（以通用逻辑为准）

备注：如果用户获得的礼物，选择直接打赏，则聊天室/直播间弹幕，不会显示用户获得了xxxx礼物。这里依据具体的活动逻辑。

# 三、展示逻辑补充

## 3.1记录

参考设计页面展示，分1次及10次样式展示

分为探索记录、珍珠记录（时间按照开始的时间）

## 3.2规则

备注：榜单规则跳转到总规则

两处规则均支持kelvin更新文本；

## 3.3至宝展示

在页面中，存在两处至宝（礼物）的展示

1.  当前至宝
    

即用户此时返回，可以获得的礼物中，价值最高的那一个礼物。这里不显示数量，如果多个礼物价值相同，则按照奖励配置中，选择靠前的；

2.  刻度线至宝
    

即按照用户当前选择的探宝仪质量，在不同深度时，触发的奖励。在对应的奖励中价值最高的那一个礼物。这里不显示数量，如果多个礼物价值相同，则按照奖励配置中，选择靠前的；

3.  至宝的切换：
    

刻度线至宝按照上边缘计算，如果上边缘和当前深度接触，则当前直播切换为触碰的这个刻度线至宝；

## 3.4补充情况

1.  用户参与活动时，页面存在倒计时，这里需要保持所有用户的倒计时一致，且用户切换后台，倒计时需要正常计时；
    
2.  查看他人：
    
    1.  用户参与时（或旁观视角），可以查看其它用户情况，包括剩余人数（未结束的），结束的用户返回的位置；
        
    2.  本轮结束时，可以查看本轮的结果（本轮结束是，在页面的用户才会弹出弹窗）
        

# 四、数据需求

【需求目的】监测活动在线数据

【取数周期】长期

【需求数据】

1、资金：资金账户流入/流出、活动总收入、礼物赠送金额、页面购买道具金额、活动总支出、直送流水、下发至背包金额

2、参与方式

1.  获取道具：获取道具总人数、获取道具总金额、赠送礼物人数、赠送礼物金额、页面购买道具人数、页面购买道具金额
    
2.  获得礼物：获得礼物总人数、获得礼物总金额、礼物进入背包人数、礼物进入背包金额、礼物直送人数、礼物直送金额
    

3、道具库存（这里不需要按照时间维度）

1.  当前有道具的总人数、总个数
    
2.  道具分层人数：1-9，10-49，50-99，100-199，200-499，500-999，1000-1999，2000-4999，5000-9999，10000以上
    

3、参与类型

1.  筛选项：入门、普通、优异、卓越、超凡、传奇、总计
    
2.  数据维度：参与人数、次数、参与金额、人均参与金额、成功次数（获得非兜底奖励）、成功人数、成功金额、次日/3日/7日复购人数（这个只看总计即可）
    

4、奖励维度

1.  奖励分为兜底、1，2，3，4，5，6，总共7个级别
    
2.  需要查看每个级别的获取人数、获取次数、金额、并且可以筛选（入门、普通、优异、卓越、超凡、传奇、总计）
    

5、礼物明细：每个礼物获得人数、个数

需要每日明细及根据筛选时间段汇总