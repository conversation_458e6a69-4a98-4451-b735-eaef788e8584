# 【谁是海王】活动开发计划

## 项目概述

【谁是海王】是一个全服实时参与的潜艇寻宝游戏活动。用户使用珍珠参与游戏，潜艇按照系统随机的最大深度下潜，用户可以随时选择返回获得奖励，深度越深奖励越好，但超过最大深度会失败。

## 技术栈和架构

- **框架**: Vue2 + Vuex + Vue Router
- **样式**: SCSS模块化
- **状态管理**: 参考项目现有 `home/store.js` 格式
- **接口对接**: 严格使用接口文档中提供的真实接口
- **组件架构**: 页面专属组件归属页面模块，通用组件独立设计

## 页面模块设计

### 1. 主游戏页面 (MainGame)

#### 页面状态转换图

```mermaid
stateDiagram-v2
    state "游戏主流程" as GameFlow {
        [*] --> 未参与
        未参与 --> 等待下潜 : 选择档位参与
        等待下潜 --> 下潜中 : 等待时间结束
        下潜中 --> 用户返回 : 用户点击返回
        下潜中 --> 自动结算 : 达到最大深度
        用户返回 --> 查看结果 : 结算完成
        自动结算 --> 查看结果 : 结算完成
        查看结果 --> 未参与 : 开始新一轮
        
        state 下潜中 {
            实时深度更新 --> 实时深度更新 : 轮询获取深度
            实时深度更新 --> 奖励更新 : 深度变化触发
        }
        
        state 查看结果 {
            [*] --> 展示奖励
            展示奖励 --> 选择处理方式 : 用户操作
            选择处理方式 --> 进入背包 : 选择背包
            选择处理方式 --> 直送打赏 : 选择直送
        }
    }
```

#### 页面布局描述

```json
{
  "MainGame": {
    "header": {
      "backButton": "返回按钮",
      "title": "谁是海王",
      "rulesButton": "规则按钮",
      "recordsButton": "记录按钮"
    },
    "userInfoSection": {
      "avatar": "用户头像",
      "nickname": "用户昵称", 
      "pearlCount": "珍珠数量显示",
      "addPearlButton": "添加珍珠按钮"
    },
    "submarineContainer": {
      "depthDisplay": "当前深度显示",
      "submarineAnimation": "潜艇动画区域",
      "depthLevels": "深度刻度线和奖励展示",
      "currentTreasure": "当前至宝展示"
    },
    "participantInfo": {
      "roundInfo": "本轮信息（轮次ID、参与人数）",
      "countdown": "倒计时显示",
      "stageIndicator": "阶段指示器（等待/下潜/结算）"
    },
    "actionSection": {
      "levelSelector": "档位选择器",
      "participateButton": "参与按钮",
      "returnButton": "返回按钮（下潜中显示）",
      "statusText": "状态提示文字"
    },
    "bottomTabs": {
      "exploreRecords": "探索记录标签",
      "pearlRecords": "珍珠记录标签",
      "rankingList": "排行榜标签"
    }
  }
}
```

#### 交互逻辑描述

**未参与状态交互（全局状态：等待开始）**
- **档位选择器**: 
  1. 用户选择探宝仪质量档位（入门/普通/优异/卓越/超凡/传奇）
  2. 根据选择更新需要消耗的珍珠数量显示
  3. 更新对应档位的奖励预览
  4. 校验用户珍珠数量是否足够

- **参与按钮**: 
  1. 检查用户是否有足够珍珠
  2. 调用 `/deep/sea/treasureHunt` 接口参与游戏
  3. 成功后更新用户状态为等待下潜，禁用参与按钮
  4. 失败时显示错误提示

**等待下潜状态交互（全局状态：等待时间）**
- **倒计时显示**: 
  1. 通过 `/deep/sea/diveRounding` 接口轮询获取等待剩余时间
  2. 实时更新倒计时显示
  3. 倒计时结束时自动切换到下潜状态

**下潜中状态交互（全局状态：下潜进行中）**
- **深度实时更新**: 
  1. 轮询 `/deep/sea/submarineBaseInfo` 获取当前深度
  2. 更新潜艇位置动画
  3. 更新深度刻度线显示
  4. 更新当前至宝显示

- **返回按钮**: 
  1. 用户点击返回确认弹窗
  2. 调用 `/deep/sea/settleTreasure` 接口主动结算
  3. 成功后切换到结果查看状态
  4. 失败时显示错误提示

**结算状态交互（全局状态：结算中）**
- **自动结算**: 
  1. 轮询检测到下潜结束时间
  2. 自动调用 `/deep/sea/exploring` 接口获取结果
  3. 展示结算结果弹窗
  4. 更新用户状态为查看结果

### 2. 记录页面 (Records)

#### 页面布局描述

```json
{
  "Records": {
    "tabsContainer": {
      "exploreTab": "探索记录标签",
      "pearlTab": "珍珠记录标签"
    },
    "exploreRecordsPanel": {
      "recordsList": "探索记录列表",
      "recordItem": {
        "timeInfo": "探索时间",
        "depthInfo": "下潜深度和等级",
        "rewardInfo": "获得奖励展示",
        "statusBadge": "成功/失败状态标识"
      },
      "loadMoreButton": "加载更多按钮"
    },
    "pearlRecordsPanel": {
      "recordsList": "珍珠记录列表", 
      "recordItem": {
        "timeInfo": "获取时间",
        "sourceInfo": "来源方式（打赏/购买）",
        "quantityInfo": "获得数量",
        "giftInfo": "相关礼物信息"
      },
      "loadMoreButton": "加载更多按钮"
    }
  }
}
```

#### 交互逻辑描述

**探索记录标签交互**
- **记录列表加载**: 
  1. 调用 `/deep/sea/exploreRecords` 接口获取探索历史
  2. 分页展示记录列表，每页20条
  3. 支持下拉刷新和上拉加载更多
  4. 空状态时显示暂无记录提示

**珍珠记录标签交互**
- **珍珠记录加载**: 
  1. 调用 `/deep/sea/assetRecords` 接口获取珍珠记录
  2. 分页展示记录列表，每页20条
  3. 区分不同来源类型的样式显示
  4. 支持滚动加载更多

### 3. 排行榜页面 (Ranking)

**注意**: 排行榜相关接口在当前接口文档中缺失

#### 页面布局描述

```json
{
  "Ranking": {
    "tabsContainer": {
      "seaKingTab": "海王榜标签",
      "seaAreaTab": "海域榜标签"
    },
    "rankingPanel": {
      "topThree": "前三名特殊展示区域",
      "rankingList": "排行榜列表",
      "rankItem": {
        "rankNumber": "排名数字",
        "userInfo": "用户信息（头像、昵称）",
        "scoreInfo": "积分信息（总深度）",
        "rewardInfo": "奖励信息"
      }
    },
    "myRankInfo": "我的排名信息"
  }
}
```

#### 交互逻辑描述

**海王榜标签交互（依赖待提供的排行榜接口）**
- **排行榜加载**: 
  1. 调用待提供的海王榜接口获取排名数据
  2. 展示用户周榜排名和总深度
  3. 展示奖励信息和领取状态
  4. 临时使用模拟数据展示界面结构

**海域榜标签交互（依赖待提供的排行榜接口）**
- **房间排名加载**: 
  1. 调用待提供的海域榜接口获取房间排名
  2. 按房间总深度展示排名
  3. 临时使用模拟数据展示界面结构

### 4. 通用组件设计

#### 结算结果弹窗组件 (SettlementModal)
- 展示本轮探索结果
- 显示获得的奖励列表和总价值
- 提供进入背包或直送选择
- 支持排名和成功人数显示

#### 倒计时组件 (CountdownTimer)
- 支持不同阶段的倒计时显示
- 自动轮询更新时间
- 支持时间结束回调

#### 礼物展示组件 (GiftDisplay)
- 统一的礼物图标和信息展示
- 支持数量和价值显示
- 支持不同尺寸和样式

#### 深度刻度线组件 (DepthScale)
- 展示深度刻度和对应奖励
- 支持当前深度高亮
- 动态更新至宝显示

## 状态管理设计

### DeepSeaStore模块

**管理的数据类型和范围**:
- 用户基本信息（珍珠数量、参与状态等）
- 游戏状态（当前轮次、深度、阶段等）
- 配置信息（档位配置、深度奖励配置等）
- 记录数据（探索记录、珍珠记录缓存）

**调用的接口和参数**:
- `getBaseInfo()`: 调用 `/deep/sea/baseInfo` 获取用户基础信息
- `getSubmarineInfo()`: 调用 `/deep/sea/submarineBaseInfo` 获取潜艇状态
- `getDiveStatus()`: 调用 `/deep/sea/diveRounding` 获取下潜进度
- `participateGame(consumerLevel)`: 调用 `/deep/sea/treasureHunt` 参与游戏
- `settleTreasure()`: 调用 `/deep/sea/settleTreasure` 主动结算
- `getExploreResult(roundId)`: 调用 `/deep/sea/exploring` 获取结果

**数据处理逻辑描述**:
- 实时轮询更新游戏状态，处理不同阶段的数据同步
- 缓存用户记录数据，支持分页加载和数据更新
- 处理游戏状态转换，确保前端状态与后端同步
- 管理倒计时状态，自动触发状态切换

## 路由设计

```javascript
// 新增活动路由配置
{
  path: '/deep-sea',
  name: 'DeepSea',
  component: () => import('@/pages/deep-sea/index.vue'),
  children: [
    {
      path: '',
      name: 'DeepSeaMain',
      component: () => import('@/pages/deep-sea/main/index.vue')
    },
    {
      path: 'records',
      name: 'DeepSeaRecords', 
      component: () => import('@/pages/deep-sea/records/index.vue')
    },
    {
      path: 'ranking',
      name: 'DeepSeaRanking',
      component: () => import('@/pages/deep-sea/ranking/index.vue')
    }
  ]
}
```

## AI代码生成任务清单

### 基础架构任务
- [ ] 生成活动路由配置，添加深海寻宝路由
- [ ] 生成DeepSeaStore状态管理模块，按照home/store.js格式实现
- [ ] 生成活动主入口页面框架
- [ ] 配置活动相关的环境参数和常量

### 核心页面任务组

#### 主游戏页面任务
- [ ] 生成主游戏页面结构和样式，实现潜艇下潜动画效果
- [ ] 实现档位选择和参与游戏功能，集成参与和结算接口
- [ ] 实现实时深度显示和倒计时功能，通过轮询保持状态同步
- [ ] 实现结算结果弹窗组件，支持奖励展示和处理选择
- [ ] 实现深度刻度线和至宝展示组件
- [ ] 集成基础信息、潜艇状态、下潜进度等API接口调用

#### 记录页面任务
- [ ] 生成记录页面结构和样式，实现标签切换功能
- [ ] 实现探索记录列表组件，支持分页加载和状态展示
- [ ] 实现珍珠记录列表组件，支持来源类型区分显示
- [ ] 集成探索记录和珍珠记录API接口调用
- [ ] 实现下拉刷新和上拉加载更多功能

#### 排行榜页面任务（依赖待提供的排行榜接口）
- [ ] 生成排行榜页面结构和样式，实现榜单切换功能
- [ ] 实现海王榜展示组件，临时使用模拟数据
- [ ] 实现海域榜展示组件，临时使用模拟数据
- [ ] 实现我的排名信息展示组件
- [ ] 预留排行榜接口对接结构，待接口提供后替换模拟数据

### 通用组件任务
- [ ] 实现倒计时组件，支持不同阶段的时间显示和自动更新
- [ ] 实现礼物展示组件，统一奖励和礼物的展示样式
- [ ] 实现确认弹窗组件，支持返回确认等操作
- [ ] 实现加载状态组件，处理接口请求的loading状态
- [ ] 实现错误提示组件，统一错误信息展示

### 集成和优化任务
- [ ] 实现页面间导航和数据传递
- [ ] 实现错误处理和网络异常处理机制
- [ ] 实现页面性能优化，处理轮询和动画性能
- [ ] 实现响应式适配，确保各种屏幕尺寸正常显示
- [ ] 集成活动入口，从礼物面板横幅跳转到活动页面

## 关键技术要点

### 实时数据同步
- 使用定时轮询保持游戏状态同步
- 处理网络异常时的状态恢复
- 优化轮询频率，平衡实时性和性能

### 状态管理复杂性
- 区分全局游戏状态和用户个人状态
- 处理状态转换的边界情况
- 确保前端状态与后端数据一致性

### 动画和交互体验
- 潜艇下潜的流畅动画效果
- 深度变化的视觉反馈
- 倒计时和状态切换的平滑过渡

### 接口依赖处理
- 现有接口：优先实现核心游戏流程
- 缺失接口：排行榜功能使用模拟数据，预留接口结构
- 错误处理：完善接口异常和网络错误的用户提示

---

该计划严格遵循项目规范，使用接口文档中的真实接口，根据业务逻辑设计合理的页面状态转换，为AI代码生成提供清晰的指导方向。 