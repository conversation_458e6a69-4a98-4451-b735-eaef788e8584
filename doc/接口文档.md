# 谁是海王活动接口文档

## 1. 首页基本信息
**接口地址**: `/deep/sea/baseInfo`  
**请求方式**: GET  
**功能说明**: 获取用户珍珠数量和探宝仪配置信息

### 请求参数
无

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| assetQuantity | long | 珍珠数量 |
| assetLevels | Array | 探宝仪配置列表 |
| └─ consumerLevel | int | 等级(1=入门,2=普通,3=优异,4=卓越,5=超凡,6=传奇) |
| └─ assetQuantity | long | 数量 |
| └─ levelName | string | 等级名称 |
| userInfo | Object | 用户信息 |
| └─ deepLevel | int | 下潜等级 |
| └─ gift | Object | 获取的礼物 |
| └─ nickname | string | 用户昵称 |
| └─ avatar | string | 用户头像 |
| └─ depth | int | 下潜深度 |
| └─ uid | string | 用户ID |

---

## 2. 探索记录
**接口地址**: `/deep/sea/exploreRecords`  
**请求方式**: POST  
**功能说明**: 获取用户探索历史记录

### 请求参数
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| anchor | long | 否 | 锚点ID，获取该ID之前的数据 |
| pageSize | int | 否 | 每页大小，默认20 |

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| list | Array | 探索记录列表 |
| └─ giftList | Array | 奖励礼物列表 |
| └─ totalDiamond | long | 获取的总价值 |
| └─ deepLevel | int | 深度等级 |
| └─ assetQuantity | int | 消费数量 |
| └─ playStatus | int | 探索状态(0=进行中,1=失败,2=成功) |
| └─ consumeLevel | int | 消费等级 |
| └─ nickname | string | 受赏人昵称 |
| └─ createTime | long | 创建时间 |
| └─ depth | int | 下潜深度 |
| count | long | 列表数量 |
| end | boolean | 是否最后一页 |

---

## 3. 潜艇基本信息
**接口地址**: `/deep/sea/submarineBaseInfo`  
**请求方式**: GET  
**功能说明**: 获取当前轮次潜艇的基本信息

### 请求参数
无

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalDiamond | long | 本轮必得价值 |
| roundId | long | 轮次ID |
| explorerQuantity | int | 参与人数 |
| targetDepth | int | 下潜的最大深度 |
| deepLevels | Array | 深度配置信息 |
| └─ giftIcon | string | 礼物图标 |
| └─ giftDiamond | long | 礼物价值 |
| └─ speed | int | 速度 |
| └─ minDepth | int | 最小深度 |
| └─ maxDepth | int | 最大深度 |
| └─ maxValue | boolean | 是否最大礼物价值 |
| currentTreasure | Object | 当前至宝 |
| └─ giftName | string | 礼物名称 |
| └─ quantity | int | 礼物数量 |
| └─ price | long | 礼物价值 |
| └─ icon | string | 礼物图标 |
| currentDepth | int | 用户当前深度 |
| currentDeepLevel | int | 当前深度等级 |
| maxValue | boolean | 至宝是最大价值 |
| userInfo | Object | 用户信息 |

---

## 4. 下潜中潜艇信息
**接口地址**: `/deep/sea/diveRounding`  
**请求方式**: GET  
**功能说明**: 获取潜艇下潜过程中的实时信息

### 请求参数
无

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| diveCountDown | long | 下潜剩余时间 |
| waitCountDown | long | 等待下潜剩余时间 |
| settleCountDown | long | 结算剩余时间 |
| stage | int | 阶段(1=等待,2=下潜,3=结算) |

---

## 5. 用户探索结束信息
**接口地址**: `/deep/sea/exploring`  
**请求方式**: POST  
**功能说明**: 获取用户探索结束后的结果信息

### 请求参数
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| roundId | long | 是 | 轮次ID |

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| awardList | Array | 奖品列表 |
| └─ giftName | string | 礼物名称 |
| └─ quantity | int | 礼物数量 |
| └─ price | long | 礼物价值 |
| └─ icon | string | 礼物图标 |
| playType | int | 游戏状态(0=进行中,1=失败,2=成功) |
| totalDiamond | long | 总价值 |
| consumerLevel | int | 消费等级 |
| successQuantity | int | 寻宝成功人数 |
| explorerQuantity | int | 参与人数 |
| depth | int | 下潜深度 |
| rank | int | 本轮排名 |

---

## 6. 参与寻宝
**接口地址**: `/deep/sea/treasureHunt`  
**请求方式**: POST  
**功能说明**: 用户参与寻宝活动

### 请求参数
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| consumerLevel | int | 是 | 消费等级 |

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| result | boolean | 参与是否成功 |

---

## 7. 结算寻宝
**接口地址**: `/deep/sea/settleTreasure`  
**请求方式**: POST  
**功能说明**: 用户主动结算寻宝

### 请求参数
无

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| result | boolean | 结算是否成功 |

---

## 8. 珍珠记录
**接口地址**: `/deep/sea/assetRecords`  
**请求方式**: POST  
**功能说明**: 获取珍珠获取和消费记录

### 请求参数
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| anchor | long | 否 | 锚点ID，获取该ID之前的数据 |
| pageSize | int | 否 | 每页大小，默认20 |

### 响应字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| list | Array | 珍珠记录列表 |
| └─ giftName | string | 礼物名称 |
| └─ giftIcon | string | 礼物图标 |
| └─ quantity | int | 礼物数量 |
| └─ createTime | long | 创建时间 |
| └─ type | int | 类型(1=打赏礼物获得,2=购买获得) |
| count | long | 列表数量 |
| end | boolean | 是否最后一页 |

---

## 通用响应格式

所有接口都遵循统一的响应格式：

```json
{
  "tid": "string",           // 链路追踪ID
  "code": "string",          // 业务状态码，8000表示成功
  "msg": "string",           // 错误信息，成功时为空
  "success": boolean,        // 请求是否成功
  "result": {},              // 业务数据
  "ext": {}                  // 扩展信息
}
```

### 状态码说明
- `8000`: 请求成功
- 其他状态码: 业务异常，具体错误信息见 `msg` 字段