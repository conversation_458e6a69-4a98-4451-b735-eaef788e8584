{"foe.globalStyleFiles": ["src/common/styles/index.scss", "src/common/styles/global/base/_common.scss", "src/common/styles/global/base/_mobile.scss"], "files.associations": {"durka.json": "jsonc"}, "eslint.validate": ["javascript", "javascriptreact", "vue"], "cSpell.words": ["apng", "eruda", "preload", "prerender"], "eslint.format.enable": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "dbaeumer.vscode-eslint"}, "[vue]": {"editor.defaultFormatter": "octref.vetur"}, "vetur.format.defaultFormatter.js": "none", "dot-template-vscode.dtplFolderName": ".isaac/dtpl"}